Video Title: 1_8_Converting_IDs_into_Dimensions
Video ID: pangU73dtc4
Language: French (translated)
--------------------------------------------------

Genre: légendes
Langue: en
Cette leçon consiste à créer
Cette leçon consiste à créer
Cette leçon consiste à créer
Dimensions des numéros d'identification. Si cela semble
Dimensions des numéros d'identification. Si cela semble
Dimensions des numéros d'identification. Si cela semble
Comme des lettres chinoises à vous, je vais
Comme des lettres chinoises à vous, je vais
Comme des lettres chinoises à vous, je vais
Expliquez ce que je veux dire. Premièrement, nous devons
Expliquez ce que je veux dire. Premièrement, nous devons
Expliquez ce que je veux dire. Premièrement, nous devons
Accédez à notre vue de données et modifiez le
Accédez à notre vue de données et modifiez le
Accédez à notre vue de données et modifiez le
Achat Table aux abonnements
Achat Table aux abonnements
Achat Table aux abonnements
tableau. Je veux que tu passais une attention particulière
tableau. Je veux que tu passais une attention particulière
tableau. Je veux que tu passais une attention particulière
à la colonne de type d'abonnement. Au
à la colonne de type d'abonnement. Au
à la colonne de type d'abonnement. Au
moment, si nous élargissons les valeurs à l'intérieur
moment, si nous élargissons les valeurs à l'intérieur
moment, si nous élargissons les valeurs à l'intérieur
La colonne, nous pouvons voir 0 2 et trois.
La colonne, nous pouvons voir 0 2 et trois.
La colonne, nous pouvons voir 0 2 et trois.
Ce sont des identifiants qui ont un sens pour le
Ce sont des identifiants qui ont un sens pour le
Ce sont des identifiants qui ont un sens pour le
personne qui a conçu la table. À
personne qui a conçu la table. À
personne qui a conçu la table. À
Tout le monde, c'est complètement inconnu
Tout le monde, c'est complètement inconnu
Tout le monde, c'est complètement inconnu
Ce que cela signifie. Pour l'utilisateur final, ils sont
Ce que cela signifie. Pour l'utilisateur final, ils sont
Ce que cela signifie. Pour l'utilisateur final, ils sont
Juste des chiffres. Je veux dire, bien sûr, on peut dire
Juste des chiffres. Je veux dire, bien sûr, on peut dire
Juste des chiffres. Je veux dire, bien sûr, on peut dire
Nous avons x de nombreux abonnés avec une pièce d'identité de
Nous avons x de nombreux abonnés avec une pièce d'identité de
Nous avons x de nombreux abonnés avec une pièce d'identité de
zéro, y abonnés d'un identifiant de deux et
zéro, y abonnés d'un identifiant de deux et
zéro, y abonnés d'un identifiant de deux et
etc. Mais quand même, notre utilisateur final aura besoin
etc. Mais quand même, notre utilisateur final aura besoin
etc. Mais quand même, notre utilisateur final aura besoin
avoir un dictionnaire en main pour séparer
avoir un dictionnaire en main pour séparer
avoir un dictionnaire en main pour séparer
L'ID Zero, Id Two et ID3. Que faire
L'ID Zero, Id Two et ID3. Que faire
L'ID Zero, Id Two et ID3. Que faire
Ils signifient? C'est pourquoi nous avons besoin d'un
Ils signifient? C'est pourquoi nous avons besoin d'un
Ils signifient? C'est pourquoi nous avons besoin d'un
meilleure façon d'expliquer cela. Donc,
meilleure façon d'expliquer cela. Donc,
meilleure façon d'expliquer cela. Donc,
Je vais demander à Chad GPT quoi faire
Je vais demander à Chad GPT quoi faire
Je vais demander à Chad GPT quoi faire
À ce sujet. Id2 signifie annuel, ID3 est
À ce sujet. Id2 signifie annuel, ID3 est
À ce sujet. Id2 signifie annuel, ID3 est
mensuellement, et zéro est à vie. Nous avons besoin d'un
mensuellement, et zéro est à vie. Nous avons besoin d'un
mensuellement, et zéro est à vie. Nous avons besoin d'un
Solution de Chad GPT
Solution de Chad GPT
Solution de Chad GPT
DÈS QUE POSSIBLE. Alors je vais dire à Chad GPT
DÈS QUE POSSIBLE. Alors je vais dire à Chad GPT
DÈS QUE POSSIBLE. Alors je vais dire à Chad GPT
informations et voir quelle solution elle
informations et voir quelle solution elle
informations et voir quelle solution elle
prévoit pour nous.
Il nous donne donc deux solutions. Le
Il nous donne donc deux solutions. Le
Il nous donne donc deux solutions. Le
Le premier comprend une colonne calculée.
Le premier comprend une colonne calculée.
Le premier comprend une colonne calculée.
Le second contient une mesure.
Le second contient une mesure.
Le second contient une mesure.
Les mesures ne conviennent pas au travail
Les mesures ne conviennent pas au travail
Les mesures ne conviennent pas au travail
avec des valeurs de texte à moins que nous ne recherchons
avec des valeurs de texte à moins que nous ne recherchons
avec des valeurs de texte à moins que nous ne recherchons
Pour des moyens de générer du texte dynamique
Pour des moyens de générer du texte dynamique
Pour des moyens de générer du texte dynamique
Basé sur le tableau de bord
Basé sur le tableau de bord
Basé sur le tableau de bord
interaction. Ce n'est pas le cas. Alors je
interaction. Ce n'est pas le cas. Alors je
interaction. Ce n'est pas le cas. Alors je
utilisera l'option de colonne calculée
utilisera l'option de colonne calculée
utilisera l'option de colonne calculée
Et une fois que nous l'avons mis en œuvre, je demanderai
Et une fois que nous l'avons mis en œuvre, je demanderai
Et une fois que nous l'avons mis en œuvre, je demanderai
Chad GPT pour expliquer la différence
Chad GPT pour expliquer la différence
Chad GPT pour expliquer la différence
entre les colonnes et les mesures calculées.
entre les colonnes et les mesures calculées.
entre les colonnes et les mesures calculées.
Je vais d'abord copier le code et j'irai
Je vais d'abord copier le code et j'irai
Je vais d'abord copier le code et j'irai
Retour à Powerbi et collez-le à l'intérieur d'un
Retour à Powerbi et collez-le à l'intérieur d'un
Retour à Powerbi et collez-le à l'intérieur d'un
Nouveau calculé
colonne. On dirait qu'il y a une erreur de faute
colonne. On dirait qu'il y a une erreur de faute
colonne. On dirait qu'il y a une erreur de faute
sur le nom de la table. Nous avons besoin d'abonnements
sur le nom de la table. Nous avons besoin d'abonnements
sur le nom de la table. Nous avons besoin d'abonnements
Tableau non abonnement. Je vais faire
Tableau non abonnement. Je vais faire
Tableau non abonnement. Je vais faire
un test très rapide pour s'assurer que le
un test très rapide pour s'assurer que le
un test très rapide pour s'assurer que le
Les valeurs sont affichées correctement. Nous avons
Les valeurs sont affichées correctement. Nous avons
Les valeurs sont affichées correctement. Nous avons
J'ai obtenu une vie annuelle, à vie et mensuellement. Tous
J'ai obtenu une vie annuelle, à vie et mensuellement. Tous
J'ai obtenu une vie annuelle, à vie et mensuellement. Tous
Ça a l'air bien. D'accord. Euh maintenant, créons un
Ça a l'air bien. D'accord. Euh maintenant, créons un
Ça a l'air bien. D'accord. Euh maintenant, créons un
Slicer en utilisant le texte du type d'abonnement
Slicer en utilisant le texte du type d'abonnement
Slicer en utilisant le texte du type d'abonnement
colonne que nous venons de créer. Je vais sélectionner
colonne que nous venons de créer. Je vais sélectionner
colonne que nous venons de créer. Je vais sélectionner
Le texte de type abonnements. Puis changer
Le texte de type abonnements. Puis changer
Le texte de type abonnements. Puis changer
la visualisation à un
la visualisation à un
la visualisation à un
Slicer. Je vais le redimensionner et le déplacer
Slicer. Je vais le redimensionner et le déplacer
Slicer. Je vais le redimensionner et le déplacer
à côté des revenus par rapport
à côté des revenus par rapport
à côté des revenus par rapport
remboursement visuel.
remboursement visuel.
remboursement visuel.
Ouais. Voyons si cela fonctionne. H nous sommes
Ouais. Voyons si cela fonctionne. H nous sommes
Ouais. Voyons si cela fonctionne. H nous sommes
Sélection de différents types d'abonnement
Sélection de différents types d'abonnement
Sélection de différents types d'abonnement
Et le visuel ne fonctionne pas.
Et le visuel ne fonctionne pas.
Et le visuel ne fonctionne pas.
C'est une bonne pratique générale de toujours
C'est une bonne pratique générale de toujours
C'est une bonne pratique générale de toujours
Testez tout ce que vous faites dans Powerbi.
Testez tout ce que vous faites dans Powerbi.
Testez tout ce que vous faites dans Powerbi.
N'attendez jamais d'avoir terminé le
N'attendez jamais d'avoir terminé le
N'attendez jamais d'avoir terminé le
Tableau de bord puis pour démarrer
Tableau de bord puis pour démarrer
Tableau de bord puis pour démarrer
essai. Je sais quel est le problème et je
essai. Je sais quel est le problème et je
essai. Je sais quel est le problème et je
vous l'expliquera dans le prochain
vous l'expliquera dans le prochain
vous l'expliquera dans le prochain
vidéo. Nous allons conclure celui-ci
vidéo. Nous allons conclure celui-ci
vidéo. Nous allons conclure celui-ci
en posant une question pour discuter de GPT pour
en posant une question pour discuter de GPT pour
en posant une question pour discuter de GPT pour
Expliquez la différence entre les mesures
Expliquez la différence entre les mesures
Expliquez la différence entre les mesures
et les colonnes calculées.
De cette explication, je veux que vous
De cette explication, je veux que vous
De cette explication, je veux que vous
Rappelez-vous deux choses sur calculé
Rappelez-vous deux choses sur calculé
Rappelez-vous deux choses sur calculé
colonnes. Le numéro un est qu'un calculé
colonnes. Le numéro un est qu'un calculé
colonnes. Le numéro un est qu'un calculé
La colonne est quelque chose qui est calculé
La colonne est quelque chose qui est calculé
La colonne est quelque chose qui est calculé
Rowby Row pour les calculs au niveau des lignes.
Rowby Row pour les calculs au niveau des lignes.
Rowby Row pour les calculs au niveau des lignes.
Ils sont utilisés pour des scénarios où vous
Ils sont utilisés pour des scénarios où vous
Ils sont utilisés pour des scénarios où vous
veulent créer une nouvelle dimension ou
veulent créer une nouvelle dimension ou
veulent créer une nouvelle dimension ou
attribut. Les mesures sont utilisées pour
attribut. Les mesures sont utilisées pour
attribut. Les mesures sont utilisées pour
agréger et résumer les données à travers
agréger et résumer les données à travers
agréger et résumer les données à travers
plusieurs lignes ou groupes de données. Ils sont
plusieurs lignes ou groupes de données. Ils sont
plusieurs lignes ou groupes de données. Ils sont
dynamique et calculé à la volée contrairement
dynamique et calculé à la volée contrairement
dynamique et calculé à la volée contrairement
colonnes calculées. Une fois que vous avez construit le
colonnes calculées. Une fois que vous avez construit le
colonnes calculées. Une fois que vous avez construit le
colonne calculée, il reste à l'intérieur du
colonne calculée, il reste à l'intérieur du
colonne calculée, il reste à l'intérieur du
Ensemble de données comme faisant partie du tableau et prend
Ensemble de données comme faisant partie du tableau et prend
Ensemble de données comme faisant partie du tableau et prend
Espace et stockage pour le garder là.
Espace et stockage pour le garder là.
Espace et stockage pour le garder là.
Chose intéressante, à la fin du
Chose intéressante, à la fin du
Chose intéressante, à la fin du
Réponse, le GPD Chad nous a fourni un
Réponse, le GPD Chad nous a fourni un
Réponse, le GPD Chad nous a fourni un
Explication de notre cas d'utilisation particulier
Explication de notre cas d'utilisation particulier
Explication de notre cas d'utilisation particulier
Scénario, ce qui est assez bon. J'ai
Scénario, ce qui est assez bon. J'ai
Scénario, ce qui est assez bon. J'ai
Dire que je suis surpris.
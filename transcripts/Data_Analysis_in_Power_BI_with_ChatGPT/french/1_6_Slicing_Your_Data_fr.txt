Video Title: 1_6_Slicing_Your_Data
Video ID: OOQaI7e5QGU
Language: French (translated)
--------------------------------------------------

Genre: légendes
Langue: en
Les rapports doivent être intuitifs et faciles à
Les rapports doivent être intuitifs et faciles à
Les rapports doivent être intuitifs et faciles à
naviguer. Nous ne pouvons pas avoir toutes les données
naviguer. Nous ne pouvons pas avoir toutes les données
naviguer. Nous ne pouvons pas avoir toutes les données
points affichés à nos utilisateurs finaux
points affichés à nos utilisateurs finaux
points affichés à nos utilisateurs finaux
fois parce que cela va se confondre et
fois parce que cela va se confondre et
fois parce que cela va se confondre et
frustrer l'utilisateur au point où
frustrer l'utilisateur au point où
frustrer l'utilisateur au point où
Ils ne prendraient pas la peine d'utiliser le rapport.
Ils ne prendraient pas la peine d'utiliser le rapport.
Ils ne prendraient pas la peine d'utiliser le rapport.
Après tout, l'idée derrière chaque rapport
Après tout, l'idée derrière chaque rapport
Après tout, l'idée derrière chaque rapport
consiste à simplifier la narration des données. Lequel
consiste à simplifier la narration des données. Lequel
consiste à simplifier la narration des données. Lequel
c'est pourquoi dans cette leçon, nous allons
c'est pourquoi dans cette leçon, nous allons
c'est pourquoi dans cette leçon, nous allons
Apprenez à filtrer ou à trancher dans Powerbi
Apprenez à filtrer ou à trancher dans Powerbi
Apprenez à filtrer ou à trancher dans Powerbi
terminologie.
terminologie.
terminologie.
Je vais demander au graphique GPT comment filtrer
Je vais demander au graphique GPT comment filtrer
Je vais demander au graphique GPT comment filtrer
nos données et je vous dirai pourquoi
nos données et je vous dirai pourquoi
nos données et je vous dirai pourquoi
Le filtrage n'est pas tout à fait le bon
Le filtrage n'est pas tout à fait le bon
Le filtrage n'est pas tout à fait le bon
Terminologie en ce qui concerne
Terminologie en ce qui concerne
Terminologie en ce qui concerne
Powerbi. Nous voulons donc avoir une sorte de
Powerbi. Nous voulons donc avoir une sorte de
Powerbi. Nous voulons donc avoir une sorte de
un filtre sur la page qui nous permettra
un filtre sur la page qui nous permettra
un filtre sur la page qui nous permettra
Pour sélectionner n'importe quelle plage de dates et notre visuel
Pour sélectionner n'importe quelle plage de dates et notre visuel
Pour sélectionner n'importe quelle plage de dates et notre visuel
devrait s'ajuster en conséquence. Alors tu sais
devrait s'ajuster en conséquence. Alors tu sais
devrait s'ajuster en conséquence. Alors tu sais
le foret. Je reviendrai à Powerbi et
le foret. Je reviendrai à Powerbi et
le foret. Je reviendrai à Powerbi et
poser la question. Je dirai le graphique
poser la question. Je dirai le graphique
poser la question. Je dirai le graphique
est trop grand et a trop de dates.
Y a-t-il un moyen de filtrer mon
Y a-t-il un moyen de filtrer mon
Y a-t-il un moyen de filtrer mon
Données à une période spécifique dans l'exemple
Données à une période spécifique dans l'exemple
Données à une période spécifique dans l'exemple
mois entre et
etc. En termes Powerbi, cela signifie que
etc. En termes Powerbi, cela signifie que
etc. En termes Powerbi, cela signifie que
le filtre sera appliqué dans le
le filtre sera appliqué dans le
le filtre sera appliqué dans le
arrière-plan et l'utilisateur final ne sera pas
arrière-plan et l'utilisateur final ne sera pas
arrière-plan et l'utilisateur final ne sera pas
avoir un accès direct au filtre lui-même.
avoir un accès direct au filtre lui-même.
avoir un accès direct au filtre lui-même.
Ils verront les données filtrées mais seulement
Ils verront les données filtrées mais seulement
Ils verront les données filtrées mais seulement
à la plage de dates spécifique qui était
à la plage de dates spécifique qui était
à la plage de dates spécifique qui était
donné par le rapport de rapport. Nous voulons
donné par le rapport de rapport. Nous voulons
donné par le rapport de rapport. Nous voulons
Donnez à nos utilisateurs la possibilité de filtrer
Donnez à nos utilisateurs la possibilité de filtrer
Donnez à nos utilisateurs la possibilité de filtrer
leurs données autant que possible. Quoi
leurs données autant que possible. Quoi
leurs données autant que possible. Quoi
Powerbi suggère être d'ajouter le filtre
Powerbi suggère être d'ajouter le filtre
Powerbi suggère être d'ajouter le filtre
À l'intérieur de ce menu Filtres. Mais ceci
À l'intérieur de ce menu Filtres. Mais ceci
À l'intérieur de ce menu Filtres. Mais ceci
Le menu des filtres n'est pas toujours disponible pour
Le menu des filtres n'est pas toujours disponible pour
Le menu des filtres n'est pas toujours disponible pour
l'utilisateur final. C'est aussi très difficile
l'utilisateur final. C'est aussi très difficile
l'utilisateur final. C'est aussi très difficile
pour naviguer surtout lorsque vous voulez
pour naviguer surtout lorsque vous voulez
pour naviguer surtout lorsque vous voulez
Sélectionnez une plage de dates spécifique. Ce genre
Sélectionnez une plage de dates spécifique. Ce genre
Sélectionnez une plage de dates spécifique. Ce genre
du filtre n'est pas très utile à la fin
du filtre n'est pas très utile à la fin
du filtre n'est pas très utile à la fin
l'utilisateur sauf s'il y a un spécifique
l'utilisateur sauf s'il y a un spécifique
l'utilisateur sauf s'il y a un spécifique
exigence pour que ce rapport soit toujours
exigence pour que ce rapport soit toujours
exigence pour que ce rapport soit toujours
dans une plage spécifique. Par exemple, pour
dans une plage spécifique. Par exemple, pour
dans une plage spécifique. Par exemple, pour
Afficher seulement 12 mois civils.
Afficher seulement 12 mois civils.
Afficher seulement 12 mois civils.
C'est une situation appropriée où nous
C'est une situation appropriée où nous
C'est une situation appropriée où nous
peut vouloir utiliser ce filtre. Pour tout
peut vouloir utiliser ce filtre. Pour tout
peut vouloir utiliser ce filtre. Pour tout
Autre but, nous utilisons quelque chose appelé
Autre but, nous utilisons quelque chose appelé
Autre but, nous utilisons quelque chose appelé
Slicers. Alors demandons un tranchage
Slicers. Alors demandons un tranchage
Slicers. Alors demandons un tranchage
Chat de la solution
Gbt et cette fois, cela nous donne le
Gbt et cette fois, cela nous donne le
Gbt et cette fois, cela nous donne le
bonne réponse. Nous commençons d'abord par
bonne réponse. Nous commençons d'abord par
bonne réponse. Nous commençons d'abord par
sélection de la visualisation de l'entonnoir et
sélection de la visualisation de l'entonnoir et
sélection de la visualisation de l'entonnoir et
Ensuite, nous ajoutons la date. Nous sélectionnons donc le
Ensuite, nous ajoutons la date. Nous sélectionnons donc le
Ensuite, nous ajoutons la date. Nous sélectionnons donc le
Slicer et nous faisons glisser l'achat
date. Maintenant, nous pouvons sélectionner librement n'importe quelle date
date. Maintenant, nous pouvons sélectionner librement n'importe quelle date
date. Maintenant, nous pouvons sélectionner librement n'importe quelle date
période que nous voulons et notre graphique
période que nous voulons et notre graphique
période que nous voulons et notre graphique
Réfléchissez à ces changements. Ce que je vais faire
Réfléchissez à ces changements. Ce que je vais faire
Réfléchissez à ces changements. Ce que je vais faire
Ensuite, c'est redimensionner le visuel et le
Ensuite, c'est redimensionner le visuel et le
Ensuite, c'est redimensionner le visuel et le
Slicer pour faire de l'espace pour l'autre
Slicer pour faire de l'espace pour l'autre
Slicer pour faire de l'espace pour l'autre
graphiques et filtres. Cela peut être réalisé
graphiques et filtres. Cela peut être réalisé
graphiques et filtres. Cela peut être réalisé
en traînant dans le coin du
en traînant dans le coin du
en traînant dans le coin du
visuel et redimensionnement où
visuel et redimensionnement où
visuel et redimensionnement où
nécessaire. Une bonne pratique pour
nécessaire. Une bonne pratique pour
nécessaire. Une bonne pratique pour
La comptabilité est de garder les filtres dans
La comptabilité est de garder les filtres dans
La comptabilité est de garder les filtres dans
un endroit. Donc tous les filtres
un endroit. Donc tous les filtres
un endroit. Donc tous les filtres
résider sur le côté droit.
résider sur le côté droit.
résider sur le côté droit.
Parfait.
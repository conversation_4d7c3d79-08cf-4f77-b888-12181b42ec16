Video Title: 1_9_Fixing_Slicers
Video ID: P7esVZENjLg
Language: French (translated)
--------------------------------------------------

Genre: légendes
Langue: en
Dans notre leçon précédente, nous avons appris comment
Dans notre leçon précédente, nous avons appris comment
Dans notre leçon précédente, nous avons appris comment
pour attribuer un sens aux colonnes numériques par
pour attribuer un sens aux colonnes numériques par
pour attribuer un sens aux colonnes numériques par
Création d'une colonne personnalisée qui convertit
Création d'une colonne personnalisée qui convertit
Création d'une colonne personnalisée qui convertit
Nombres dans le texte. Nous avons pu résoudre
Nombres dans le texte. Nous avons pu résoudre
Nombres dans le texte. Nous avons pu résoudre
le puzzle à mi-chemin uniquement parce que notre
le puzzle à mi-chemin uniquement parce que notre
le puzzle à mi-chemin uniquement parce que notre
Slicer ne tranchait pas nos données. Faisons
Slicer ne tranchait pas nos données. Faisons
Slicer ne tranchait pas nos données. Faisons
Jetez un œil au code Dax qui était
Jetez un œil au code Dax qui était
Jetez un œil au code Dax qui était
nous donnés par Chad Gpt. Donc, il utilise
nous donnés par Chad Gpt. Donc, il utilise
nous donnés par Chad Gpt. Donc, il utilise
une fonction de commutation qui est fondamentalement un
une fonction de commutation qui est fondamentalement un
une fonction de commutation qui est fondamentalement un
Si déclaration avec beaucoup de déclarations si
Si déclaration avec beaucoup de déclarations si
Si déclaration avec beaucoup de déclarations si
à l'intérieur. Le code dit que pour le
à l'intérieur. Le code dit que pour le
à l'intérieur. Le code dit que pour le
colonne de type d'abonnement, toutes les instances
colonne de type d'abonnement, toutes les instances
colonne de type d'abonnement, toutes les instances
de zéro nous donnera une vie. Tous
de zéro nous donnera une vie. Tous
de zéro nous donnera une vie. Tous
Des instances de deux nous donneront annuelle et
Des instances de deux nous donneront annuelle et
Des instances de deux nous donneront annuelle et
Toutes les instances de trois nous donneront
Toutes les instances de trois nous donneront
Toutes les instances de trois nous donneront
mensuel. Tout le reste est
mensuel. Tout le reste est
mensuel. Tout le reste est
inconnu. C'est clair et simple, mais
inconnu. C'est clair et simple, mais
inconnu. C'est clair et simple, mais
Cela ne fonctionne toujours pas. Et la raison
Cela ne fonctionne toujours pas. Et la raison
Cela ne fonctionne toujours pas. Et la raison
Derrière cela, nous utilisons plusieurs
Derrière cela, nous utilisons plusieurs
Derrière cela, nous utilisons plusieurs
tableaux. Le visuel qui représente le net
tableaux. Le visuel qui représente le net
tableaux. Le visuel qui représente le net
Les revenus contre les remboursements proviennent du
Les revenus contre les remboursements proviennent du
Les revenus contre les remboursements proviennent du
table d'achat. Et notre abonnement
table d'achat. Et notre abonnement
table d'achat. Et notre abonnement
vient du tableau des abonnements.
vient du tableau des abonnements.
vient du tableau des abonnements.
Ces deux tables ne sont pas directement liées
Ces deux tables ne sont pas directement liées
Ces deux tables ne sont pas directement liées
les uns aux autres. Nous avons les étudiants
les uns aux autres. Nous avons les étudiants
les uns aux autres. Nous avons les étudiants
table qui se trouve en plein milieu. Donc
table qui se trouve en plein milieu. Donc
table qui se trouve en plein milieu. Donc
Nous devons dire à nos tables de propager
Nous devons dire à nos tables de propager
Nous devons dire à nos tables de propager
directement à travers la table des élèves et
directement à travers la table des élèves et
directement à travers la table des élèves et
accepter les conditions de filtrage. Ces
accepter les conditions de filtrage. Ces
accepter les conditions de filtrage. Ces
Les relations entre les tables ont
Les relations entre les tables ont
Les relations entre les tables ont
J'ai une et une étoile. Ce qui signifie que où
J'ai une et une étoile. Ce qui signifie que où
J'ai une et une étoile. Ce qui signifie que où
Nous en avons un à la fin du
Nous en avons un à la fin du
Nous en avons un à la fin du
relation, le tableau peut filtrer le
relation, le tableau peut filtrer le
relation, le tableau peut filtrer le
table à l'autre extrémité où nous avons le
étoile. C'est ce qu'on appelle un à beaucoup
étoile. C'est ce qu'on appelle un à beaucoup
étoile. C'est ce qu'on appelle un à beaucoup
relation. Il y a aussi une flèche
relation. Il y a aussi une flèche
relation. Il y a aussi une flèche
qui pointe quel tableau sera
qui pointe quel tableau sera
qui pointe quel tableau sera
filtré. Alors les élèves sont donc des étudiants
filtré. Alors les élèves sont donc des étudiants
filtré. Alors les élèves sont donc des étudiants
peut filtrer les deux achats et
peut filtrer les deux achats et
peut filtrer les deux achats et
abonnements. Ce que nous voulons, c'est
abonnements. Ce que nous voulons, c'est
abonnements. Ce que nous voulons, c'est
abonnements pour pouvoir filtrer
abonnements pour pouvoir filtrer
abonnements pour pouvoir filtrer
étudiants. Ensuite, le filtrage s'appliquera
étudiants. Ensuite, le filtrage s'appliquera
étudiants. Ensuite, le filtrage s'appliquera
sur la table d'achat également. Pour réparer
sur la table d'achat également. Pour réparer
sur la table d'achat également. Pour réparer
Ceci, nous devons faire un clic droit sur le
Ceci, nous devons faire un clic droit sur le
Ceci, nous devons faire un clic droit sur le
relation et sélectionner les propriétés sur
relation et sélectionner les propriétés sur
relation et sélectionner les propriétés sur
Le menu où il indique un filtre croisé
Le menu où il indique un filtre croisé
Le menu où il indique un filtre croisé
direction, réglez-le sur les deux et frappez bien.
direction, réglez-le sur les deux et frappez bien.
direction, réglez-le sur les deux et frappez bien.
Maintenant, testons notre
Slicer. Nous pouvons sélectionner annuel, nous pouvons
Slicer. Nous pouvons sélectionner annuel, nous pouvons
Slicer. Nous pouvons sélectionner annuel, nous pouvons
Sélectionnez mensuellement et nous pouvons sélectionner
Sélectionnez mensuellement et nous pouvons sélectionner
Sélectionnez mensuellement et nous pouvons sélectionner
durée de vie. Ils produisent tous des tranches de
durée de vie. Ils produisent tous des tranches de
durée de vie. Ils produisent tous des tranches de
les données.
Video Title: 1_11_Churn_Calculations
Video ID: 0Wlz3WUotuU
Language: French (translated)
--------------------------------------------------

Genre: légendes
Langue: en
Il est maintenant temps pour nous de commencer à travailler
Il est maintenant temps pour nous de commencer à travailler
Il est maintenant temps pour nous de commencer à travailler
sur notre deuxième tableau qui montre le désabonnement
sur notre deuxième tableau qui montre le désabonnement
sur notre deuxième tableau qui montre le désabonnement
taux. Les informations de désabonnement seront obtenues
taux. Les informations de désabonnement seront obtenues
taux. Les informations de désabonnement seront obtenues
de notre table d'abonnements. Depuis
de notre table d'abonnements. Depuis
de notre table d'abonnements. Depuis
le désabonnement a à voir avec le nombre
le désabonnement a à voir avec le nombre
le désabonnement a à voir avec le nombre
Les abonnés que nous avons décidés ont décidé
Les abonnés que nous avons décidés ont décidé
Les abonnés que nous avons décidés ont décidé
pour mettre fin à leur abonnement. Premièrement, nous sommes
pour mettre fin à leur abonnement. Premièrement, nous sommes
pour mettre fin à leur abonnement. Premièrement, nous sommes
Je vais parler de chat sur notre
Je vais parler de chat sur notre
Je vais parler de chat sur notre
table d'abonnements avec tous les
table d'abonnements avec tous les
table d'abonnements avec tous les
colonnes disponibles à l'intérieur pour que le bot
colonnes disponibles à l'intérieur pour que le bot
colonnes disponibles à l'intérieur pour que le bot
est familier avec tous les
est familier avec tous les
est familier avec tous les
colonnes. Comme vous pouvez le voir, j'ai commencé
colonnes. Comme vous pouvez le voir, j'ai commencé
colonnes. Comme vous pouvez le voir, j'ai commencé
poser la question. Je dis à Chad
poser la question. Je dis à Chad
poser la question. Je dis à Chad
Gpt que j'ai une table d'abonnements
Gpt que j'ai une table d'abonnements
Gpt que j'ai une table d'abonnements
Avec les noms de colonne suivants, annulez
Avec les noms de colonne suivants, annulez
Avec les noms de colonne suivants, annulez
Date, date créée, date de fin, etc.
Date, date créée, date de fin, etc.
Date, date créée, date de fin, etc.
Alors je vais énumérer toute la colonne
Alors je vais énumérer toute la colonne
Alors je vais énumérer toute la colonne
noms comme nous l'avons fait précédemment pour que
noms comme nous l'avons fait précédemment pour que
noms comme nous l'avons fait précédemment pour que
Le chatbot est pleinement conscient du type
Le chatbot est pleinement conscient du type
Le chatbot est pleinement conscient du type
des colonnes de données que nous avons à l'intérieur de notre
des colonnes de données que nous avons à l'intérieur de notre
des colonnes de données que nous avons à l'intérieur de notre
Tableau d'abonnements. Alors ça peut
Tableau d'abonnements. Alors ça peut
Tableau d'abonnements. Alors ça peut
nous suggérer la meilleure solution DAX optimale
nous suggérer la meilleure solution DAX optimale
nous suggérer la meilleure solution DAX optimale
pour notre taux de désabonnement.
D'accord, donc le chatbot semble avoir
D'accord, donc le chatbot semble avoir
D'accord, donc le chatbot semble avoir
compris toutes les colonnes. Il est ajouté
compris toutes les colonnes. Il est ajouté
compris toutes les colonnes. Il est ajouté
Quelques descriptions contre ce que chacun
Quelques descriptions contre ce que chacun
Quelques descriptions contre ce que chacun
la colonne est ou ce qu'il pense réellement
la colonne est ou ce qu'il pense réellement
la colonne est ou ce qu'il pense réellement
Chaque colonne est. Super jusqu'à présent. Puis je
Chaque colonne est. Super jusqu'à présent. Puis je
Chaque colonne est. Super jusqu'à présent. Puis je
lui demandera de faire le taux de désabonnement
lui demandera de faire le taux de désabonnement
lui demandera de faire le taux de désabonnement
calcul.
Il suggère trois calculs. Un pour
Il suggère trois calculs. Un pour
Il suggère trois calculs. Un pour
Count de désabonnement, un pour les abonnements, et
Count de désabonnement, un pour les abonnements, et
Count de désabonnement, un pour les abonnements, et
un troisième pour diviser le nombre de choyages
un troisième pour diviser le nombre de choyages
un troisième pour diviser le nombre de choyages
contre les abonnements totaux. FAIT CECI
contre les abonnements totaux. FAIT CECI
contre les abonnements totaux. FAIT CECI
Tout a du sens? D'accord,
Tout a du sens? D'accord,
Tout a du sens? D'accord,
bien. Il y a un léger problème avec ça
bien. Il y a un léger problème avec ça
bien. Il y a un léger problème avec ça
calcul. Il s'appuie sur l'État
calcul. Il s'appuie sur l'État
calcul. Il s'appuie sur l'État
colonne pour contenir des choses comme
colonne pour contenir des choses comme
colonne pour contenir des choses comme
annulé. Vérifions notre colonne d'État
annulé. Vérifions notre colonne d'État
annulé. Vérifions notre colonne d'État
d'abord. Pour vérifier cela, nous devons revenir en arrière
d'abord. Pour vérifier cela, nous devons revenir en arrière
d'abord. Pour vérifier cela, nous devons revenir en arrière
à Powerbi. Cliquez sur les abonnements
à Powerbi. Cliquez sur les abonnements
à Powerbi. Cliquez sur les abonnements
tableau. Ensuite, allez à la vue de données et
tableau. Ensuite, allez à la vue de données et
tableau. Ensuite, allez à la vue de données et
Allons à l'endroit où la colonne d'état
Allons à l'endroit où la colonne d'état
Allons à l'endroit où la colonne d'état
est. Il semble que ce soit un nombre. Donc
est. Il semble que ce soit un nombre. Donc
est. Il semble que ce soit un nombre. Donc
L'utilisation d'un nombre ne nous donnera pas de bien
L'utilisation d'un nombre ne nous donnera pas de bien
L'utilisation d'un nombre ne nous donnera pas de bien
Parce que nous ne savons pas ce que
Parce que nous ne savons pas ce que
Parce que nous ne savons pas ce que
les nombres signifient. Alors je vais retourner à
les nombres signifient. Alors je vais retourner à
les nombres signifient. Alors je vais retourner à
tracer GPT et expliquer que cet état
tracer GPT et expliquer que cet état
tracer GPT et expliquer que cet état
la colonne ne nous sert à rien parce que
la colonne ne nous sert à rien parce que
la colonne ne nous sert à rien parce que
c'est en fait un nombre et nous ne pouvons pas dire
c'est en fait un nombre et nous ne pouvons pas dire
c'est en fait un nombre et nous ne pouvons pas dire
quel état est canlé ou fondamentalement nous
quel état est canlé ou fondamentalement nous
quel état est canlé ou fondamentalement nous
ne peut pas réaliser le code DAX suggéré.
ne peut pas réaliser le code DAX suggéré.
ne peut pas réaliser le code DAX suggéré.
Alors voyons ce que le chat GPD a atteint
Alors voyons ce que le chat GPD a atteint
Alors voyons ce que le chat GPD a atteint
Dites à ce sujet.
Donc, il est dit que si la colonne d'état dans votre
Donc, il est dit que si la colonne d'état dans votre
Donc, il est dit que si la colonne d'état dans votre
L'abonnement est représenté comme un nombre,
L'abonnement est représenté comme un nombre,
L'abonnement est représenté comme un nombre,
vous pouvez modifier la mesure du nombre de désabonnement
vous pouvez modifier la mesure du nombre de désabonnement
vous pouvez modifier la mesure du nombre de désabonnement
pour s'adapter à cela. D'accord. Alors c'est
pour s'adapter à cela. D'accord. Alors c'est
pour s'adapter à cela. D'accord. Alors c'est
suggérant qu'il utilise le numéro deux,
suggérant qu'il utilise le numéro deux,
suggérant qu'il utilise le numéro deux,
Mais nous ne savons pas. Nous n'avons pas de
Mais nous ne savons pas. Nous n'avons pas de
Mais nous ne savons pas. Nous n'avons pas de
dictionnaire. Nous ne savons pas quel état
dictionnaire. Nous ne savons pas quel état
dictionnaire. Nous ne savons pas quel état
le numéro deux est. Nous ne savons pas quel état
le numéro deux est. Nous ne savons pas quel état
le numéro deux est. Nous ne savons pas quel état
Le numéro un est. Nous ne savons pas quel état
Le numéro un est. Nous ne savons pas quel état
Le numéro un est. Nous ne savons pas quel état
Le numéro trois est. Nous n'avons aucun de
Le numéro trois est. Nous n'avons aucun de
Le numéro trois est. Nous n'avons aucun de
ces informations. Encore une fois, ce n'est pas
ces informations. Encore une fois, ce n'est pas
ces informations. Encore une fois, ce n'est pas
nous servir le but. Alors je vais
nous servir le but. Alors je vais
nous servir le but. Alors je vais
Demandez-lui de suggérer un meilleur calcul
Demandez-lui de suggérer un meilleur calcul
Demandez-lui de suggérer un meilleur calcul
Parce que la colonne d'état n'est pas un
Parce que la colonne d'état n'est pas un
Parce que la colonne d'état n'est pas un
option pour nous à utiliser.
D'accord. Encore une fois, euh, il n'utilise que le
D'accord. Encore une fois, euh, il n'utilise que le
D'accord. Encore une fois, euh, il n'utilise que le
Annuler la date où la date d'annulation n'est pas
Annuler la date où la date d'annulation n'est pas
Annuler la date où la date d'annulation n'est pas
vide.
vide.
vide.
D'accord. Um cela suggère également le démarrage du désabonnement
D'accord. Um cela suggère également le démarrage du désabonnement
D'accord. Um cela suggère également le démarrage du désabonnement
et la date de fin de désabonnement. Maintenant je suis un peu
et la date de fin de désabonnement. Maintenant je suis un peu
et la date de fin de désabonnement. Maintenant je suis un peu
sceptique à propos de ces deux parce que je
sceptique à propos de ces deux parce que je
sceptique à propos de ces deux parce que je
ne pense pas qu'il y ait des colonnes que nous
ne pense pas qu'il y ait des colonnes que nous
ne pense pas qu'il y ait des colonnes que nous
se sont mis à l'intérieur de notre ensemble de données. Mais bon
se sont mis à l'intérieur de notre ensemble de données. Mais bon
se sont mis à l'intérieur de notre ensemble de données. Mais bon
Essayons-le et voyons comment chat gpt
Essayons-le et voyons comment chat gpt
Essayons-le et voyons comment chat gpt
a compris la mission. Alors je suis
a compris la mission. Alors je suis
a compris la mission. Alors je suis
aller copier le code pour le désabonnement
aller copier le code pour le désabonnement
aller copier le code pour le désabonnement
compter. Donc le premier. D'accord. So Copie
compter. Donc le premier. D'accord. So Copie
compter. Donc le premier. D'accord. So Copie
le
le
le
Code et puis je vais retourner à
Code et puis je vais retourner à
Code et puis je vais retourner à
Powerbi et testez-le pour voir si
Powerbi et testez-le pour voir si
Powerbi et testez-le pour voir si
fonctionne réellement. Pour faire ça, je vais
fonctionne réellement. Pour faire ça, je vais
fonctionne réellement. Pour faire ça, je vais
Allez chez vous et sélectionnez une nouvelle mesure.
Allez chez vous et sélectionnez une nouvelle mesure.
Allez chez vous et sélectionnez une nouvelle mesure.
Dans notre nouvelle mesure, je vais
Dans notre nouvelle mesure, je vais
Dans notre nouvelle mesure, je vais
Collez le code. Ctrl + v.
Collez le code. Ctrl + v.
Collez le code. Ctrl + v.
Et comme je l'ai suspect, comme je l'ai soupçonné,
Et comme je l'ai suspect, comme je l'ai soupçonné,
Et comme je l'ai suspect, comme je l'ai soupçonné,
Date de début de désabonnement et de la date de fin de barat
Date de début de désabonnement et de la date de fin de barat
Date de début de désabonnement et de la date de fin de barat
n'existe pas. D'accord, ces colonnes ne sont pas
n'existe pas. D'accord, ces colonnes ne sont pas
n'existe pas. D'accord, ces colonnes ne sont pas
partie de notre ensemble de données. Donc, ce n'est pas le troisième
partie de notre ensemble de données. Donc, ce n'est pas le troisième
partie de notre ensemble de données. Donc, ce n'est pas le troisième
Temps chanceux, mais quatrième fois chanceux. Donc,
Temps chanceux, mais quatrième fois chanceux. Donc,
Temps chanceux, mais quatrième fois chanceux. Donc,
Nous allons retourner pour discuter GPT et
Nous allons retourner pour discuter GPT et
Nous allons retourner pour discuter GPT et
Expliquez que nous n'avons pas de démarrage de graphique
Expliquez que nous n'avons pas de démarrage de graphique
Expliquez que nous n'avons pas de démarrage de graphique
Date et désabonnement Date de fin. Donc pas de désabonnement
Date et désabonnement Date de fin. Donc pas de désabonnement
Date et désabonnement Date de fin. Donc pas de désabonnement
Date de début Aucune date de désabonnement, donnez-nous un
Date de début Aucune date de désabonnement, donnez-nous un
Date de début Aucune date de désabonnement, donnez-nous un
code qui est réellement pertinent pour le
code qui est réellement pertinent pour le
code qui est réellement pertinent pour le
Ensemble de données que nous avons car sinon
Ensemble de données que nous avons car sinon
Ensemble de données que nous avons car sinon
Nous ne pouvons pas terminer notre calcul. Donc
Nous ne pouvons pas terminer notre calcul. Donc
Nous ne pouvons pas terminer notre calcul. Donc
Ok, voyons ce que Charg suggérera
Ok, voyons ce que Charg suggérera
Ok, voyons ce que Charg suggérera
ce
ce
ce
le temps encore, cela suggère d'avoir trois
le temps encore, cela suggère d'avoir trois
le temps encore, cela suggère d'avoir trois
calculs un pour le taux de désabonnement un sous
calculs un pour le taux de désabonnement un sous
calculs un pour le taux de désabonnement un sous
pour les abonnements et un autre pour
pour les abonnements et un autre pour
pour les abonnements et un autre pour
le taux de désabonnement réel. Donc nous avons le barattage
le taux de désabonnement réel. Donc nous avons le barattage
le taux de désabonnement réel. Donc nous avons le barattage
compter, un pour le nombre de désabonnement, un pour
compter, un pour le nombre de désabonnement, un pour
compter, un pour le nombre de désabonnement, un pour
abonnements totaux, et un autre pour
abonnements totaux, et un autre pour
abonnements totaux, et un autre pour
taux de désabonnement. Alors je vais copier le
taux de désabonnement. Alors je vais copier le
taux de désabonnement. Alors je vais copier le
Churn Count One. Je vais remplacer
Churn Count One. Je vais remplacer
Churn Count One. Je vais remplacer
le nombre de désabonnement existant. D'accord, tellement
le nombre de désabonnement existant. D'accord, tellement
le nombre de désabonnement existant. D'accord, tellement
Loin, nous ne voyons aucune erreur. Copier
Loin, nous ne voyons aucune erreur. Copier
Loin, nous ne voyons aucune erreur. Copier
celui des abonnements totaux. Je suis
celui des abonnements totaux. Je suis
celui des abonnements totaux. Je suis
va copier cela également et créer un
va copier cela également et créer un
va copier cela également et créer un
nouvelle mesure pour les abonnements totaux.
Collez ça. D'accord. Et nous nous retrouvons avec
Collez ça. D'accord. Et nous nous retrouvons avec
Collez ça. D'accord. Et nous nous retrouvons avec
une dernière mesure et c'est le
une dernière mesure et c'est le
une dernière mesure et c'est le
mesure qui calcule le désabonnement réel
mesure qui calcule le désabonnement réel
mesure qui calcule le désabonnement réel
taux. D'accord. Nous retournons donc et copiez le
taux. D'accord. Nous retournons donc et copiez le
taux. D'accord. Nous retournons donc et copiez le
Mesure Dax finale. Nous copierons cela. Nous
Mesure Dax finale. Nous copierons cela. Nous
Mesure Dax finale. Nous copierons cela. Nous
Créez une nouvelle mesure pour notre taux de désabonnement.
Créez une nouvelle mesure pour notre taux de désabonnement.
Créez une nouvelle mesure pour notre taux de désabonnement.
D'accord. Donc Ctrl + V pour le coller. Et regarde
D'accord. Donc Ctrl + V pour le coller. Et regarde
D'accord. Donc Ctrl + V pour le coller. Et regarde
Comme jusqu'à présent, nous n'avons rencontré aucun
Comme jusqu'à présent, nous n'avons rencontré aucun
Comme jusqu'à présent, nous n'avons rencontré aucun
erreurs.
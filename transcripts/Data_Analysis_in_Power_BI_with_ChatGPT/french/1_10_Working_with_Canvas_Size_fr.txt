Video Title: 1_10_Working_with_Canvas_Size
Video ID: 7bj3zOSX7xY
Language: French (translated)
--------------------------------------------------

Genre: légendes
Langue: en
Nos visuels commencent à paraître de plus en plus
Nos visuels commencent à paraître de plus en plus
Nos visuels commencent à paraître de plus en plus
comme un vrai rapport. Le problème est que nous
comme un vrai rapport. Le problème est que nous
comme un vrai rapport. Le problème est que nous
Je n'ai pas assez d'espace. Nous voulons
Je n'ai pas assez d'espace. Nous voulons
Je n'ai pas assez d'espace. Nous voulons
Placer un tableau de plus sur notre page, mais
Placer un tableau de plus sur notre page, mais
Placer un tableau de plus sur notre page, mais
Nous n'avons pas l'espace. Avant de montrer
Nous n'avons pas l'espace. Avant de montrer
Nous n'avons pas l'espace. Avant de montrer
vous comment faire ça, je veux ranger ceci
vous comment faire ça, je veux ranger ceci
vous comment faire ça, je veux ranger ceci
page encore plus. Ajoutons des données
page encore plus. Ajoutons des données
page encore plus. Ajoutons des données
Étiquettes à l'intérieur de notre graphique à barres. Pour faire ça,
Étiquettes à l'intérieur de notre graphique à barres. Pour faire ça,
Étiquettes à l'intérieur de notre graphique à barres. Pour faire ça,
Nous cliquons sur le visuel et allons au
Nous cliquons sur le visuel et allons au
Nous cliquons sur le visuel et allons au
Panneau de formatage. Alors, où il dit
Panneau de formatage. Alors, où il dit
Panneau de formatage. Alors, où il dit
Étiquettes de données, assurez-vous qu'elle est cochée.
Étiquettes de données, assurez-vous qu'elle est cochée.
Étiquettes de données, assurez-vous qu'elle est cochée.
Cela nous donnera quelques chiffres à l'intérieur
Cela nous donnera quelques chiffres à l'intérieur
Cela nous donnera quelques chiffres à l'intérieur
les barres pour que nous puissions voir ce que le réel
les barres pour que nous puissions voir ce que le réel
les barres pour que nous puissions voir ce que le réel
chiffres
chiffres
chiffres
sont. Ensuite, nous retournons à la construction visuelle
sont. Ensuite, nous retournons à la construction visuelle
sont. Ensuite, nous retournons à la construction visuelle
panneau et où il dit l'axe y, double
panneau et où il dit l'axe y, double
panneau et où il dit l'axe y, double
cliquez dessus et nommez-le pour
cliquez dessus et nommez-le pour
cliquez dessus et nommez-le pour
prix. Ensuite, nous avons l'abonnement
prix. Ensuite, nous avons l'abonnement
prix. Ensuite, nous avons l'abonnement
Tapez le texte Slicker. Je n'aime pas où ça
Tapez le texte Slicker. Je n'aime pas où ça
Tapez le texte Slicker. Je n'aime pas où ça
est positionné et je veux aussi changer
est positionné et je veux aussi changer
est positionné et je veux aussi changer
son style à autre chose. Alors, je
son style à autre chose. Alors, je
son style à autre chose. Alors, je
Sélectionnez la trancheuse et accédez au
Sélectionnez la trancheuse et accédez au
Sélectionnez la trancheuse et accédez au
Onglet Formatage. Puis de Slicer
Onglet Formatage. Puis de Slicer
Onglet Formatage. Puis de Slicer
Paramètres, je vais changer le style en
Paramètres, je vais changer le style en
Paramètres, je vais changer le style en
tuile. Maintenant, la liste ressemble à certains
tuile. Maintenant, la liste ressemble à certains
tuile. Maintenant, la liste ressemble à certains
boutons à l'intérieur d'un conteneur. Jouons
boutons à l'intérieur d'un conteneur. Jouons
boutons à l'intérieur d'un conteneur. Jouons
avec la hauteur et la largeur du conteneur
avec la hauteur et la largeur du conteneur
avec la hauteur et la largeur du conteneur
Jusqu'à ce que nous ajustions notre trancheuse comme nous
Jusqu'à ce que nous ajustions notre trancheuse comme nous
Jusqu'à ce que nous ajustions notre trancheuse comme nous
veux. Nous visons à avoir un élément
veux. Nous visons à avoir un élément
veux. Nous visons à avoir un élément
par rangée. Quelque chose comme ça. Maintenant je suis
par rangée. Quelque chose comme ça. Maintenant je suis
par rangée. Quelque chose comme ça. Maintenant je suis
Je vais juste le déplacer. Peut-être
Je vais juste le déplacer. Peut-être
Je vais juste le déplacer. Peut-être
redimensionner à nouveau une fois de plus.
redimensionner à nouveau une fois de plus.
redimensionner à nouveau une fois de plus.
La seule chose que je n'aime pas, c'est avoir le
La seule chose que je n'aime pas, c'est avoir le
La seule chose que je n'aime pas, c'est avoir le
En-tête de texte de type d'abonnement. Alors, je suis
En-tête de texte de type d'abonnement. Alors, je suis
En-tête de texte de type d'abonnement. Alors, je suis
aller éteindre l'en-tête de tranche
désactivé. D'accord, laissons-le là pour le moment
désactivé. D'accord, laissons-le là pour le moment
désactivé. D'accord, laissons-le là pour le moment
et voir comment nous pouvons augmenter la toile
et voir comment nous pouvons augmenter la toile
et voir comment nous pouvons augmenter la toile
Taille de notre rapport Powerbi. Je vais demander
Taille de notre rapport Powerbi. Je vais demander
Taille de notre rapport Powerbi. Je vais demander
Chad GPT pour voir si cela est possible.
D'accord. Donc, Chad GPT nous offre le
D'accord. Donc, Chad GPT nous offre le
D'accord. Donc, Chad GPT nous offre le
solution suivante. Allez à Powerbi. Sur
solution suivante. Allez à Powerbi. Sur
solution suivante. Allez à Powerbi. Sur
l'onglet Affichage et sélectionnez Affichage de la page
l'onglet Affichage et sélectionnez Affichage de la page
l'onglet Affichage et sélectionnez Affichage de la page
SECTION, puis la taille de taille de toile déroulante
SECTION, puis la taille de taille de toile déroulante
SECTION, puis la taille de taille de toile déroulante
menu. Si je vais sur la vue de la page,
menu. Si je vais sur la vue de la page,
menu. Si je vais sur la vue de la page,
Il n'y a pas de taille de toile
Il n'y a pas de taille de toile
Il n'y a pas de taille de toile
Option Dropown. Je vais dire à Chad
Option Dropown. Je vais dire à Chad
Option Dropown. Je vais dire à Chad
Gpt qu'il n'y a pas de chute de taille de toile
Gpt qu'il n'y a pas de chute de taille de toile
Gpt qu'il n'y a pas de chute de taille de toile
option.
Alors maintenant, ça nous dit qu'il n'y a pas
Alors maintenant, ça nous dit qu'il n'y a pas
Alors maintenant, ça nous dit qu'il n'y a pas
moyen de redimensionner la toile dans PowerBi.
moyen de redimensionner la toile dans PowerBi.
moyen de redimensionner la toile dans PowerBi.
Au lieu de cela, il nous dit comment optimiser
Au lieu de cela, il nous dit comment optimiser
Au lieu de cela, il nous dit comment optimiser
ce
ce
ce
page. Je n'achète pas ça. Alors j'irai
page. Je n'achète pas ça. Alors j'irai
page. Je n'achète pas ça. Alors j'irai
à Powerbi et désélectionner tout
à Powerbi et désélectionner tout
à Powerbi et désélectionner tout
visuels. Alors j'irai formater votre
visuels. Alors j'irai formater votre
visuels. Alors j'irai formater votre
Page de rapport et accédez aux paramètres de Canvas.
Page de rapport et accédez aux paramètres de Canvas.
Page de rapport et accédez aux paramètres de Canvas.
De là, où il dit type, change
De là, où il dit type, change
De là, où il dit type, change
les 16 à 9 à la coutume. De là, allez à
les 16 à 9 à la coutume. De là, allez à
les 16 à 9 à la coutume. De là, allez à
hauteur et largeur et jouer avec le
hauteur et largeur et jouer avec le
hauteur et largeur et jouer avec le
Options. Je pense qu'une hauteur de 1 00x 1280
Options. Je pense qu'une hauteur de 1 00x 1280
Options. Je pense qu'une hauteur de 1 00x 1280
ce serait
ce serait
ce serait
suffisant. Alors je vais bouger légèrement
suffisant. Alors je vais bouger légèrement
suffisant. Alors je vais bouger légèrement
Ce visuel vers le bas donc nous avons de l'espace au-dessus
Ce visuel vers le bas donc nous avons de l'espace au-dessus
Ce visuel vers le bas donc nous avons de l'espace au-dessus
pour un titre.
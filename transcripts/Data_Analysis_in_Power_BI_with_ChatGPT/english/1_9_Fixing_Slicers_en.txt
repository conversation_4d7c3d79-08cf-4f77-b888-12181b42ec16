Video Title: 1_9_Fixing_Slicers
Video ID: P7esVZENjLg
Language: English (en)
--------------------------------------------------

Kind: captions
Language: en
In our previous lesson, we learned how
In our previous lesson, we learned how
In our previous lesson, we learned how
to assign meaning to numeric columns by
to assign meaning to numeric columns by
to assign meaning to numeric columns by
creating a custom column that converts
creating a custom column that converts
creating a custom column that converts
numbers into text. We were able to solve
numbers into text. We were able to solve
numbers into text. We were able to solve
the puzzle halfway only because our
the puzzle halfway only because our
the puzzle halfway only because our
slicer was not slicing our data. Let's
slicer was not slicing our data. Let's
slicer was not slicing our data. Let's
take a look at the DAX code that was
take a look at the DAX code that was
take a look at the DAX code that was
given to us by Chad GPT. So, it is using
given to us by Chad GPT. So, it is using
given to us by Chad GPT. So, it is using
a switch function which is basically an
a switch function which is basically an
a switch function which is basically an
if statement with many if statements
if statement with many if statements
if statement with many if statements
inside. The code says that for the
inside. The code says that for the
inside. The code says that for the
subscription type column, all instances
subscription type column, all instances
subscription type column, all instances
of zero will give us lifetime. All
of zero will give us lifetime. All
of zero will give us lifetime. All
instances of two will give us annual and
instances of two will give us annual and
instances of two will give us annual and
all instances of three will give us
all instances of three will give us
all instances of three will give us
monthly. Anything else is
monthly. Anything else is
monthly. Anything else is
unknown. This is plain and simple, but
unknown. This is plain and simple, but
unknown. This is plain and simple, but
still it doesn't work. And the reason
still it doesn't work. And the reason
still it doesn't work. And the reason
behind this is that we're using multiple
behind this is that we're using multiple
behind this is that we're using multiple
tables. The visual that represents net
tables. The visual that represents net
tables. The visual that represents net
revenue versus refunds comes from the
revenue versus refunds comes from the
revenue versus refunds comes from the
purchases table. And our subscription
purchases table. And our subscription
purchases table. And our subscription
comes from the subscriptions table.
comes from the subscriptions table.
comes from the subscriptions table.
These two tables are not directly linked
These two tables are not directly linked
These two tables are not directly linked
to each other. We have the students
to each other. We have the students
to each other. We have the students
table that sits right in the middle. So
table that sits right in the middle. So
table that sits right in the middle. So
we need to tell our tables to propagate
we need to tell our tables to propagate
we need to tell our tables to propagate
directly through the students table and
directly through the students table and
directly through the students table and
accept the filtering conditions. These
accept the filtering conditions. These
accept the filtering conditions. These
relationships between the tables have
relationships between the tables have
relationships between the tables have
got a one and a star. Meaning that where
got a one and a star. Meaning that where
got a one and a star. Meaning that where
we have one on the end of the
we have one on the end of the
we have one on the end of the
relationship, the table can filter the
relationship, the table can filter the
relationship, the table can filter the
table on the other end where we have the
star. This is called one to many
star. This is called one to many
star. This is called one to many
relationship. There is also an arrow
relationship. There is also an arrow
relationship. There is also an arrow
that points which table will be
that points which table will be
that points which table will be
filtered. So students So students table
filtered. So students So students table
filtered. So students So students table
can filter both purchases and
can filter both purchases and
can filter both purchases and
subscriptions. What we want is
subscriptions. What we want is
subscriptions. What we want is
subscriptions to be able to filter
subscriptions to be able to filter
subscriptions to be able to filter
students. then the filtering will apply
students. then the filtering will apply
students. then the filtering will apply
onto the purchase table as well. To fix
onto the purchase table as well. To fix
onto the purchase table as well. To fix
this, we need to right click on the
this, we need to right click on the
this, we need to right click on the
relationship and select properties on
relationship and select properties on
relationship and select properties on
the menu where it says cross filter
the menu where it says cross filter
the menu where it says cross filter
direction, set it to both and hit okay.
direction, set it to both and hit okay.
direction, set it to both and hit okay.
Now let's test our
slicer. We can select annual, we can
slicer. We can select annual, we can
slicer. We can select annual, we can
select monthly and we can select
select monthly and we can select
select monthly and we can select
lifetime. They all produce slicing of
lifetime. They all produce slicing of
lifetime. They all produce slicing of
the data.
Video Title: 1_2_Download_PowerBI
Video ID: mtrSLJ4Y8WI
Language: English (en)
--------------------------------------------------

Kind: captions
Language: en
Before we build our PowerBI report using
Before we build our PowerBI report using
Before we build our PowerBI report using
Chart GPT, there are several
Chart GPT, there are several
Chart GPT, there are several
prerequisites. The first one is having
prerequisites. The first one is having
prerequisites. The first one is having
PowerBI installed on your machine. If
PowerBI installed on your machine. If
PowerBI installed on your machine. If
you don't have PowerBI installed on your
you don't have PowerBI installed on your
you don't have PowerBI installed on your
PC, please follow along. If not, you can
PC, please follow along. If not, you can
PC, please follow along. If not, you can
fast forward this video. To download
fast forward this video. To download
fast forward this video. To download
PowerBI, you can go to the following
PowerBI, you can go to the following
PowerBI, you can go to the following
link and choose your download options.
link and choose your download options.
link and choose your download options.
If you are using your Microsoft account
If you are using your Microsoft account
If you are using your Microsoft account
to use your PC, you can click on the
to use your PC, you can click on the
to use your PC, you can click on the
download and Windows will take you to
download and Windows will take you to
download and Windows will take you to
the Windows Store and obtain your copy
the Windows Store and obtain your copy
the Windows Store and obtain your copy
of PowerBI from the Microsoft Store. If
of PowerBI from the Microsoft Store. If
of PowerBI from the Microsoft Store. If
you choose this option, PowerBI will
you choose this option, PowerBI will
you choose this option, PowerBI will
always stay up to date. You don't have
always stay up to date. You don't have
always stay up to date. You don't have
to manually update it. I am going to
to manually update it. I am going to
to manually update it. I am going to
click on the link, and this is the
click on the link, and this is the
click on the link, and this is the
pop-up you will see when you try to
pop-up you will see when you try to
pop-up you will see when you try to
download it from the Microsoft Store. I
download it from the Microsoft Store. I
download it from the Microsoft Store. I
already have it, so I'm not going to
already have it, so I'm not going to
already have it, so I'm not going to
install it.
install it.
install it.
If you choose the advanced option,
If you choose the advanced option,
If you choose the advanced option,
Microsoft will take you to this page. I
Microsoft will take you to this page. I
Microsoft will take you to this page. I
recommend you use the English version of
recommend you use the English version of
recommend you use the English version of
PowerBI. So, I will click download. You
PowerBI. So, I will click download. You
PowerBI. So, I will click download. You
have two options for 64-bit system and a
have two options for 64-bit system and a
have two options for 64-bit system and a
general PBI desktop setup. In the
general PBI desktop setup. In the
general PBI desktop setup. In the
unusual case where you'll be using a
unusual case where you'll be using a
unusual case where you'll be using a
32-bit Windows version, you can select
32-bit Windows version, you can select
32-bit Windows version, you can select
the PBI desktop setup. But my copy is
the PBI desktop setup. But my copy is
the PBI desktop setup. But my copy is
64-bit. So I will select the 64-bit and
64-bit. So I will select the 64-bit and
64-bit. So I will select the 64-bit and
hit next. This will download the file.
hit next. This will download the file.
hit next. This will download the file.
Then what you need to do is follow the
Then what you need to do is follow the
Then what you need to do is follow the
guided user interface. This is a very
guided user interface. This is a very
guided user interface. This is a very
straightforward, pain-free installation
straightforward, pain-free installation
straightforward, pain-free installation
process. So please click next until you
process. So please click next until you
process. So please click next until you
see the install button. It is that
see the install button. It is that
see the install button. It is that
easy. Following your installation of
easy. Following your installation of
easy. Following your installation of
PowerBI, you may want to open the
PowerBI, you may want to open the
PowerBI, you may want to open the
program to see if it is running okay and
program to see if it is running okay and
program to see if it is running okay and
not showing any errors.
not showing any errors.
not showing any errors.
This is what an empty PowerBI report
This is what an empty PowerBI report
This is what an empty PowerBI report
page looks like. If this is what your
page looks like. If this is what your
page looks like. If this is what your
PowerBI looks like, then
PowerBI looks like, then
PowerBI looks like, then
congratulations. You have successfully
congratulations. You have successfully
congratulations. You have successfully
installed the PowerBI application on
installed the PowerBI application on
installed the PowerBI application on
your machine. Next, what you want to do
your machine. Next, what you want to do
your machine. Next, what you want to do
is you may want to close this empty file
is you may want to close this empty file
is you may want to close this empty file
because we already have a base file that
because we already have a base file that
because we already have a base file that
we're going to use throughout the
we're going to use throughout the
we're going to use throughout the
course. So, you may want to go to the
course. So, you may want to go to the
course. So, you may want to go to the
download section of the course and
download section of the course and
download section of the course and
download our PowerBI file. The file is
download our PowerBI file. The file is
download our PowerBI file. The file is
called student turnover report. This is
called student turnover report. This is
called student turnover report. This is
what we're going to be using throughout
what we're going to be using throughout
what we're going to be using throughout
the course. So, please make sure you
the course. So, please make sure you
the course. So, please make sure you
download this file before you proceed
download this file before you proceed
download this file before you proceed
any further with the training.
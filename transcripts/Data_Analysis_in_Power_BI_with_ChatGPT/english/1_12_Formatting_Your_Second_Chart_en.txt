Video Title: 1_12_Formatting_Your_Second_Chart
Video ID: ei2wENjPNw4
Language: English (en)
--------------------------------------------------

Kind: captions
Language: en
We have built the breadand butter
We have built the breadand butter
We have built the breadand butter
measures for our churn rate. It is now
measures for our churn rate. It is now
measures for our churn rate. It is now
time for us to display it inside a
time for us to display it inside a
time for us to display it inside a
visual. First I will select the churn
visual. First I will select the churn
visual. First I will select the churn
rate measure and PowerBI will
rate measure and PowerBI will
rate measure and PowerBI will
automatically suggest a visualization to
automatically suggest a visualization to
automatically suggest a visualization to
use. Looks like it is the bar chart. If
use. Looks like it is the bar chart. If
use. Looks like it is the bar chart. If
I hover over the bar chart, I can see
I hover over the bar chart, I can see
I hover over the bar chart, I can see
that it is showing the measure as
that it is showing the measure as
that it is showing the measure as
0.44 which is not looking for. The churn
0.44 which is not looking for. The churn
0.44 which is not looking for. The churn
rate needs to be a percentage. Let's ask
rate needs to be a percentage. Let's ask
rate needs to be a percentage. Let's ask
<PERSON> GPT how to do it. So we're asking
Chad GPT how to do it. So we're asking
Chad GPT how to do it. So we're asking
So we're telling Chad GPT that the churn
So we're telling Chad GPT that the churn
So we're telling Chad GPT that the churn
rate needs to be a
rate needs to be a
rate needs to be a
percentage. Our question is how to
percentage. Our question is how to
percentage. Our question is how to
format it into a
percentage. Judge is suggesting to use a
percentage. Judge is suggesting to use a
percentage. Judge is suggesting to use a
format function. Now for those
format function. Now for those
format function. Now for those
unfamiliar with DAX, the format function
unfamiliar with DAX, the format function
unfamiliar with DAX, the format function
will transform anything into text and
will transform anything into text and
will transform anything into text and
text does not work on visuals because
text does not work on visuals because
text does not work on visuals because
remember guys, visuals need to aggregate
remember guys, visuals need to aggregate
remember guys, visuals need to aggregate
data and we cannot aggregate text. So I
data and we cannot aggregate text. So I
data and we cannot aggregate text. So I
will go back to chat GPT and tell chat
will go back to chat GPT and tell chat
will go back to chat GPT and tell chat
GPT that the format function is likely
GPT that the format function is likely
GPT that the format function is likely
to transform our data into text.
to transform our data into text.
to transform our data into text.
So I will say is there any way to apply
So I will say is there any way to apply
So I will say is there any way to apply
the percentage formatting without
the percentage formatting without
the percentage formatting without
actually using the format
actually using the format
actually using the format
function. Let's see what chap GPT will
function. Let's see what chap GPT will
function. Let's see what chap GPT will
suggest.
And it looks like chat GPS here is being
And it looks like chat GPS here is being
And it looks like chat GPS here is being
very stubborn because again it's just
very stubborn because again it's just
very stubborn because again it's just
suggesting to use the format function.
suggesting to use the format function.
suggesting to use the format function.
Okay, I will try using the format
Okay, I will try using the format
Okay, I will try using the format
function even though I am
function even though I am
function even though I am
99.9% positive that this will not work.
99.9% positive that this will not work.
99.9% positive that this will not work.
But let's copy the code. Let's go back
But let's copy the code. Let's go back
But let's copy the code. Let's go back
to PowerBI and create a new measure to
to PowerBI and create a new measure to
to PowerBI and create a new measure to
test it out. So, I'm going to paste that
test it out. So, I'm going to paste that
test it out. So, I'm going to paste that
DAX. Okay. I'm going to select the
DAX. Okay. I'm going to select the
DAX. Okay. I'm going to select the
visual. I'm going to remove the previous
visual. I'm going to remove the previous
visual. I'm going to remove the previous
measure and I will select the CH rate
measure and I will select the CH rate
measure and I will select the CH rate
percentage. Now, of course, it doesn't
percentage. Now, of course, it doesn't
percentage. Now, of course, it doesn't
work. It doesn't work because nothing
work. It doesn't work because nothing
work. It doesn't work because nothing
shows inside the visual. Okay? Because
shows inside the visual. Okay? Because
shows inside the visual. Okay? Because
format is using text. So, I will delete
format is using text. So, I will delete
format is using text. So, I will delete
this
this
this
measure. And what I will do is I will
measure. And what I will do is I will
measure. And what I will do is I will
just drag the churn rate onto
just drag the churn rate onto
just drag the churn rate onto
the onto the
the onto the
the onto the
visual first. Let's remove that tool
visual first. Let's remove that tool
visual first. Let's remove that tool
tip. Okay. So we've got the churn rate
tip. Okay. So we've got the churn rate
tip. Okay. So we've got the churn rate
and I will change the formatting into a
and I will change the formatting into a
and I will change the formatting into a
percentage. This can be achieved from
percentage. This can be achieved from
percentage. This can be achieved from
measure tools and choose format as
measure tools and choose format as
measure tools and choose format as
percentage. Okay. So let's suggest a
percentage. Okay. So let's suggest a
percentage. Okay. So let's suggest a
visualization because we don't like the
visualization because we don't like the
visualization because we don't like the
bar chart and we want chart GPT to
bar chart and we want chart GPT to
bar chart and we want chart GPT to
suggest us what to use. Okay. So um we
suggest us what to use. Okay. So um we
suggest us what to use. Okay. So um we
want to see it on monthly
basis and it's suggesting a line chart
basis and it's suggesting a line chart
basis and it's suggesting a line chart
or a column chart. We don't want the
or a column chart. We don't want the
or a column chart. We don't want the
column chart. So we're going to stick to
column chart. So we're going to stick to
column chart. So we're going to stick to
the line chart. Okay. So it explains if
the line chart. Okay. So it explains if
the line chart. Okay. So it explains if
we're going to stick to line chart,
we're going to stick to line chart,
we're going to stick to line chart,
place month, year column onto the x-axis
place month, year column onto the x-axis
place month, year column onto the x-axis
of the chart. Add the churn rate
of the chart. Add the churn rate
of the chart. Add the churn rate
percentage onto the values. Okay, that's
percentage onto the values. Okay, that's
percentage onto the values. Okay, that's
perfectly clear. So I am going to select
perfectly clear. So I am going to select
perfectly clear. So I am going to select
our churn rate and change this
our churn rate and change this
our churn rate and change this
visualization into a line chart. Okay,
visualization into a line chart. Okay,
visualization into a line chart. Okay,
we have successfully changed the
we have successfully changed the
we have successfully changed the
visualization onto a line chart. Now I'm
visualization onto a line chart. Now I'm
visualization onto a line chart. Now I'm
going to drag the purchase date onto the
going to drag the purchase date onto the
going to drag the purchase date onto the
axis. I will remove the quarter and day
axis. I will remove the quarter and day
axis. I will remove the quarter and day
and it looks like our chart is now flat.
and it looks like our chart is now flat.
and it looks like our chart is now flat.
The churn rate is the same for each
The churn rate is the same for each
The churn rate is the same for each
month. Let's go back to the model view
month. Let's go back to the model view
month. Let's go back to the model view
and I will explain why is that. So you
and I will explain why is that. So you
and I will explain why is that. So you
see between subscriptions and students
see between subscriptions and students
see between subscriptions and students
we have a two-way relationship but
we have a two-way relationship but
we have a two-way relationship but
between students and purchases that
between students and purchases that
between students and purchases that
relationship is one way. So I will
relationship is one way. So I will
relationship is one way. So I will
change the cross filter to be both. So
change the cross filter to be both. So
change the cross filter to be both. So
now purchases and students are both
now purchases and students are both
now purchases and students are both
filtered by the same tables. Okay. So
filtered by the same tables. Okay. So
filtered by the same tables. Okay. So
our chart now looks as it should be. We
our chart now looks as it should be. We
our chart now looks as it should be. We
can see different churn rate for each
can see different churn rate for each
can see different churn rate for each
month. Okay. So, let's try to see if the
month. Okay. So, let's try to see if the
month. Okay. So, let's try to see if the
slicer will impact on our visual. And
slicer will impact on our visual. And
slicer will impact on our visual. And
looks like it does. So, the churn rate
looks like it does. So, the churn rate
looks like it does. So, the churn rate
is responsive to the purchase date
is responsive to the purchase date
is responsive to the purchase date
slicer. Same goes for revenue versus
slicer. Same goes for revenue versus
slicer. Same goes for revenue versus
refunds. Okay. So, so far so good. This
refunds. Okay. So, so far so good. This
refunds. Okay. So, so far so good. This
chart looks
chart looks
chart looks
good. And finally, we're just going to
good. And finally, we're just going to
good. And finally, we're just going to
go back to our data model view and I
go back to our data model view and I
go back to our data model view and I
will explain what we did with these
will explain what we did with these
will explain what we did with these
relationships in case you guys have
relationships in case you guys have
relationships in case you guys have
missed out. So the idea here is that we
missed out. So the idea here is that we
missed out. So the idea here is that we
want purchases to be able to be affected
want purchases to be able to be affected
want purchases to be able to be affected
by subscriptions. Subscriptions and
by subscriptions. Subscriptions and
by subscriptions. Subscriptions and
students used to be in a birectional
students used to be in a birectional
students used to be in a birectional
relationship. So we change the
relationship. So we change the
relationship. So we change the
relationship between students and
relationship between students and
relationship between students and
purchases to also be birectional. So at
purchases to also be birectional. So at
purchases to also be birectional. So at
the end of the day, the relationship
the end of the day, the relationship
the end of the day, the relationship
from purchase table can propagate all
from purchase table can propagate all
from purchase table can propagate all
the way down to subscriptions. So this
the way down to subscriptions. So this
the way down to subscriptions. So this
allows us to synchronize all three
allows us to synchronize all three
allows us to synchronize all three
tables.
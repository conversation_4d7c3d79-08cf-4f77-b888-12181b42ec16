Video Title: 1_10_Working_with_Canvas_Size
Video ID: 7bj3zOSX7xY
Language: English (en)
--------------------------------------------------

Kind: captions
Language: en
our visuals start to look more and more
our visuals start to look more and more
our visuals start to look more and more
like a real report. The problem is we
like a real report. The problem is we
like a real report. The problem is we
don't have enough space. We want to
don't have enough space. We want to
don't have enough space. We want to
place one more chart onto our page, but
place one more chart onto our page, but
place one more chart onto our page, but
we don't have the space. Before I show
we don't have the space. Before I show
we don't have the space. Before I show
you how to do that, I want to tidy this
you how to do that, I want to tidy this
you how to do that, I want to tidy this
page even more. Let's add some data
page even more. Let's add some data
page even more. Let's add some data
labels inside our bar chart. To do that,
labels inside our bar chart. To do that,
labels inside our bar chart. To do that,
we click on the visual and go to the
we click on the visual and go to the
we click on the visual and go to the
formatting panel. Then, where it says
formatting panel. Then, where it says
formatting panel. Then, where it says
data labels, make sure it is ticked on.
data labels, make sure it is ticked on.
data labels, make sure it is ticked on.
This will give us some numbers inside
This will give us some numbers inside
This will give us some numbers inside
the bars so we can see what the actual
the bars so we can see what the actual
the bars so we can see what the actual
figures
figures
figures
are. Then we go back to the build visual
are. Then we go back to the build visual
are. Then we go back to the build visual
panel and where it says Y-axis, double
panel and where it says Y-axis, double
panel and where it says Y-axis, double
click on that and name it to
click on that and name it to
click on that and name it to
price. Next, we have the subscription
price. Next, we have the subscription
price. Next, we have the subscription
type text slicer. I don't like where it
type text slicer. I don't like where it
type text slicer. I don't like where it
is positioned and I also want to change
is positioned and I also want to change
is positioned and I also want to change
its style to something else. So, I
its style to something else. So, I
its style to something else. So, I
select the slicer and go to the
select the slicer and go to the
select the slicer and go to the
formatting tab. Then from slicer
formatting tab. Then from slicer
formatting tab. Then from slicer
settings, I will change the style to
settings, I will change the style to
settings, I will change the style to
tile. Now the list looks like some
tile. Now the list looks like some
tile. Now the list looks like some
buttons inside a container. Let's play
buttons inside a container. Let's play
buttons inside a container. Let's play
with the container height and width
with the container height and width
with the container height and width
until we adjust our slicer the way we
until we adjust our slicer the way we
until we adjust our slicer the way we
want to. We're aiming to have one item
want to. We're aiming to have one item
want to. We're aiming to have one item
per row. Something like this. Now I'm
per row. Something like this. Now I'm
per row. Something like this. Now I'm
just going to move it around. Perhaps
just going to move it around. Perhaps
just going to move it around. Perhaps
resize again one more time.
resize again one more time.
resize again one more time.
The one thing I don't like is having the
The one thing I don't like is having the
The one thing I don't like is having the
subscription type text header. So, I'm
subscription type text header. So, I'm
subscription type text header. So, I'm
going to switch off the slice header
off. Okay, let's leave it there for now
off. Okay, let's leave it there for now
off. Okay, let's leave it there for now
and see how we can increase the canvas
and see how we can increase the canvas
and see how we can increase the canvas
size of our PowerBI report. I'll ask
size of our PowerBI report. I'll ask
size of our PowerBI report. I'll ask
Chad GPT to see if this is possible.
Okay. So, Chad GPT is offering us the
Okay. So, Chad GPT is offering us the
Okay. So, Chad GPT is offering us the
following solution. Go to PowerBI. On
following solution. Go to PowerBI. On
following solution. Go to PowerBI. On
the view tab and select page view
the view tab and select page view
the view tab and select page view
section, then canvas size drop-down
section, then canvas size drop-down
section, then canvas size drop-down
menu. If I go onto the page view,
menu. If I go onto the page view,
menu. If I go onto the page view,
there's no there's no canvas size
there's no there's no canvas size
there's no there's no canvas size
dropown option. I'm going to tell Chad
dropown option. I'm going to tell Chad
dropown option. I'm going to tell Chad
GPT that there is no canvas size dropown
GPT that there is no canvas size dropown
GPT that there is no canvas size dropown
option.
So now it is telling us that there is no
So now it is telling us that there is no
So now it is telling us that there is no
way to resize the canvas in PowerBI.
way to resize the canvas in PowerBI.
way to resize the canvas in PowerBI.
Instead, it tells us how to optimize
Instead, it tells us how to optimize
Instead, it tells us how to optimize
this
this
this
page. I'm not buying this. So I will go
page. I'm not buying this. So I will go
page. I'm not buying this. So I will go
to PowerBI and deselect any
to PowerBI and deselect any
to PowerBI and deselect any
visuals. Then I will go to format your
visuals. Then I will go to format your
visuals. Then I will go to format your
report page and go to canvas settings.
report page and go to canvas settings.
report page and go to canvas settings.
From there, where it says type, change
From there, where it says type, change
From there, where it says type, change
the 16 to9 to custom. From there, go to
the 16 to9 to custom. From there, go to
the 16 to9 to custom. From there, go to
height and width and play with the
height and width and play with the
height and width and play with the
options. I think a height of 1,00x 1280
options. I think a height of 1,00x 1280
options. I think a height of 1,00x 1280
would be
would be
would be
sufficient. Then I will slightly move
sufficient. Then I will slightly move
sufficient. Then I will slightly move
that visual down so we have space above
that visual down so we have space above
that visual down so we have space above
for a title.
Video Title: 1_11_Churn_Calculations
Video ID: 0Wlz3WUotuU
Language: English (en)
--------------------------------------------------

Kind: captions
Language: en
It is now time for us to start working
It is now time for us to start working
It is now time for us to start working
on our second chart that shows the churn
on our second chart that shows the churn
on our second chart that shows the churn
rate. Churn information will be obtained
rate. Churn information will be obtained
rate. Churn information will be obtained
from our subscriptions table. Since
from our subscriptions table. Since
from our subscriptions table. Since
churn has to do with how many
churn has to do with how many
churn has to do with how many
subscribers we have decided have decided
subscribers we have decided have decided
subscribers we have decided have decided
to end their subscription. First, we're
to end their subscription. First, we're
to end their subscription. First, we're
going to tell chat GPT about our
going to tell chat GPT about our
going to tell chat GPT about our
subscriptions table with all the
subscriptions table with all the
subscriptions table with all the
available columns inside so that the bot
available columns inside so that the bot
available columns inside so that the bot
is familiar with all the
is familiar with all the
is familiar with all the
columns. As you can see, I have started
columns. As you can see, I have started
columns. As you can see, I have started
asking the question. I am telling <PERSON>
asking the question. I am telling <PERSON>
asking the question. I am telling Chad
GPT that I have a subscriptions table
GPT that I have a subscriptions table
GPT that I have a subscriptions table
with the following column names, cancel
with the following column names, cancel
with the following column names, cancel
date, created date, end date, and etc.
date, created date, end date, and etc.
date, created date, end date, and etc.
So I'm going to list all the column
So I'm going to list all the column
So I'm going to list all the column
names just how we did previously so that
names just how we did previously so that
names just how we did previously so that
the chatbot is fully aware of what type
the chatbot is fully aware of what type
the chatbot is fully aware of what type
of data columns we've got inside our
of data columns we've got inside our
of data columns we've got inside our
subscriptions table. So then it can
subscriptions table. So then it can
subscriptions table. So then it can
suggest us the best optimal DAX solution
suggest us the best optimal DAX solution
suggest us the best optimal DAX solution
for our churn rate.
Okay, so the chatbot seems to have
Okay, so the chatbot seems to have
Okay, so the chatbot seems to have
understood all the columns. It's added
understood all the columns. It's added
understood all the columns. It's added
some descriptions against what each
some descriptions against what each
some descriptions against what each
column is or what it actually thinks
column is or what it actually thinks
column is or what it actually thinks
each column is. Great so far. Then I
each column is. Great so far. Then I
each column is. Great so far. Then I
will ask him to do the churn rate
will ask him to do the churn rate
will ask him to do the churn rate
calculation.
It suggests three calculations. One for
It suggests three calculations. One for
It suggests three calculations. One for
churn count, one for subscriptions, and
churn count, one for subscriptions, and
churn count, one for subscriptions, and
a third one to divide churn count
a third one to divide churn count
a third one to divide churn count
against total subscriptions. Does this
against total subscriptions. Does this
against total subscriptions. Does this
all make sense? Okay,
all make sense? Okay,
all make sense? Okay,
good. There's a slight problem with this
good. There's a slight problem with this
good. There's a slight problem with this
calculation. It relies on the state
calculation. It relies on the state
calculation. It relies on the state
column to contain things such as
column to contain things such as
column to contain things such as
cancelled. Let's check our state column
cancelled. Let's check our state column
cancelled. Let's check our state column
first. To check that, we need to go back
first. To check that, we need to go back
first. To check that, we need to go back
to PowerBI. Click on the subscriptions
to PowerBI. Click on the subscriptions
to PowerBI. Click on the subscriptions
table. Then go to the data view and
table. Then go to the data view and
table. Then go to the data view and
let's go over to where the state column
let's go over to where the state column
let's go over to where the state column
is. It looks like it is a number. So
is. It looks like it is a number. So
is. It looks like it is a number. So
using a number will not give us any good
using a number will not give us any good
using a number will not give us any good
because we don't know what any of these
because we don't know what any of these
because we don't know what any of these
numbers mean. So I'm going to go back to
numbers mean. So I'm going to go back to
numbers mean. So I'm going to go back to
chart GPT and explain that this state
chart GPT and explain that this state
chart GPT and explain that this state
column is serving us no purpose because
column is serving us no purpose because
column is serving us no purpose because
it's actually a number and we cannot say
it's actually a number and we cannot say
it's actually a number and we cannot say
which state is canled or basically we
which state is canled or basically we
which state is canled or basically we
cannot fulfill the suggested DAX code.
cannot fulfill the suggested DAX code.
cannot fulfill the suggested DAX code.
So let's see what chat GPD has got to
So let's see what chat GPD has got to
So let's see what chat GPD has got to
say about this.
So it says if the state column in your
So it says if the state column in your
So it says if the state column in your
subscription is represented as a number,
subscription is represented as a number,
subscription is represented as a number,
you can modify the churn count measure
you can modify the churn count measure
you can modify the churn count measure
to accommodate this. Okay. So it's
to accommodate this. Okay. So it's
to accommodate this. Okay. So it's
suggesting that it's using number two,
suggesting that it's using number two,
suggesting that it's using number two,
but we don't know. We don't have a
but we don't know. We don't have a
but we don't know. We don't have a
dictionary. We don't know what state
dictionary. We don't know what state
dictionary. We don't know what state
number two is. We don't know what state
number two is. We don't know what state
number two is. We don't know what state
number one is. We don't know what state
number one is. We don't know what state
number one is. We don't know what state
number three is. We don't have any of
number three is. We don't have any of
number three is. We don't have any of
this information. So again, this is not
this information. So again, this is not
this information. So again, this is not
serving us the purpose. So I'm going to
serving us the purpose. So I'm going to
serving us the purpose. So I'm going to
ask it to suggest a better calculation
ask it to suggest a better calculation
ask it to suggest a better calculation
because the state column is not an
because the state column is not an
because the state column is not an
option for us to use.
Okay. So again, um it's just using the
Okay. So again, um it's just using the
Okay. So again, um it's just using the
cancel date where cancel date is not
cancel date where cancel date is not
cancel date where cancel date is not
blank.
blank.
blank.
Okay. Um it also suggests churn start
Okay. Um it also suggests churn start
Okay. Um it also suggests churn start
and churn end date. Now I'm a bit
and churn end date. Now I'm a bit
and churn end date. Now I'm a bit
skeptical about these two because I
skeptical about these two because I
skeptical about these two because I
don't think there are columns that we
don't think there are columns that we
don't think there are columns that we
have got inside our data set. But hey
have got inside our data set. But hey
have got inside our data set. But hey
let's give it a go and see how chat GPT
let's give it a go and see how chat GPT
let's give it a go and see how chat GPT
has understood the assignment. So I am
has understood the assignment. So I am
has understood the assignment. So I am
going to copy the code for the churn
going to copy the code for the churn
going to copy the code for the churn
count. So the first one. Okay. So copy
count. So the first one. Okay. So copy
count. So the first one. Okay. So copy
the
the
the
code and then I'm going to go back to
code and then I'm going to go back to
code and then I'm going to go back to
PowerBI and test it out to see if it
PowerBI and test it out to see if it
PowerBI and test it out to see if it
actually works. To do that, I'm going to
actually works. To do that, I'm going to
actually works. To do that, I'm going to
go to home and select new measure.
go to home and select new measure.
go to home and select new measure.
Inside our new measure, I am going to
Inside our new measure, I am going to
Inside our new measure, I am going to
paste the code. Ctrl +V.
paste the code. Ctrl +V.
paste the code. Ctrl +V.
And as I have sus, as I have suspected,
And as I have sus, as I have suspected,
And as I have sus, as I have suspected,
churn start date and churn end date do
churn start date and churn end date do
churn start date and churn end date do
not exist. Okay, these columns are not
not exist. Okay, these columns are not
not exist. Okay, these columns are not
part of our data set. So, it's not third
part of our data set. So, it's not third
part of our data set. So, it's not third
time lucky, but fourth time lucky. So,
time lucky, but fourth time lucky. So,
time lucky, but fourth time lucky. So,
we're going to go back to chat GPT and
we're going to go back to chat GPT and
we're going to go back to chat GPT and
explain that we do not have chart start
explain that we do not have chart start
explain that we do not have chart start
date and churn end date. So no churn
date and churn end date. So no churn
date and churn end date. So no churn
start date no churn end date give us a
start date no churn end date give us a
start date no churn end date give us a
code that is actually relevant to the
code that is actually relevant to the
code that is actually relevant to the
data set that we have because otherwise
data set that we have because otherwise
data set that we have because otherwise
we cannot complete our calculation. So
we cannot complete our calculation. So
we cannot complete our calculation. So
okay let's see what charg will suggest
okay let's see what charg will suggest
okay let's see what charg will suggest
this
this
this
time again it is suggesting having three
time again it is suggesting having three
time again it is suggesting having three
calculations one for churn rate one sub
calculations one for churn rate one sub
calculations one for churn rate one sub
for subscriptions and another one for
for subscriptions and another one for
for subscriptions and another one for
the actual churn rate. So we have churn
the actual churn rate. So we have churn
the actual churn rate. So we have churn
count, one for churn count, one for
count, one for churn count, one for
count, one for churn count, one for
total subscriptions, and another one for
total subscriptions, and another one for
total subscriptions, and another one for
churn rate. So I'm going to copy the
churn rate. So I'm going to copy the
churn rate. So I'm going to copy the
churn count one. I'm going to replace
churn count one. I'm going to replace
churn count one. I'm going to replace
the existing churn count. Okay, so so
the existing churn count. Okay, so so
the existing churn count. Okay, so so
far we don't see any errors. Let's copy
far we don't see any errors. Let's copy
far we don't see any errors. Let's copy
the one for total subscriptions. I'm
the one for total subscriptions. I'm
the one for total subscriptions. I'm
going to copy that as well and create a
going to copy that as well and create a
going to copy that as well and create a
new measure for the total subscriptions.
paste that. Okay. And we're left with
paste that. Okay. And we're left with
paste that. Okay. And we're left with
one final measure and that is the
one final measure and that is the
one final measure and that is the
measure that calculates the actual churn
measure that calculates the actual churn
measure that calculates the actual churn
rate. Okay. So we go back and copy the
rate. Okay. So we go back and copy the
rate. Okay. So we go back and copy the
final DAX measure. We copy that. We
final DAX measure. We copy that. We
final DAX measure. We copy that. We
create a new measure for our churn rate.
create a new measure for our churn rate.
create a new measure for our churn rate.
Okay. So Ctrl +V to paste it. And looks
Okay. So Ctrl +V to paste it. And looks
Okay. So Ctrl +V to paste it. And looks
like so far we have not run into any
like so far we have not run into any
like so far we have not run into any
errors.
Video Title: 1_8_Converting_IDs_into_Dimensions
Video ID: pangU73dtc4
Language: English (en)
--------------------------------------------------

Kind: captions
Language: en
This lesson is all about creating
This lesson is all about creating
This lesson is all about creating
dimensions of ID numbers. If this sounds
dimensions of ID numbers. If this sounds
dimensions of ID numbers. If this sounds
like Chinese letters to you, I will
like Chinese letters to you, I will
like Chinese letters to you, I will
explain what I mean. First, we need to
explain what I mean. First, we need to
explain what I mean. First, we need to
go to our data view and change the
go to our data view and change the
go to our data view and change the
purchases table to the subscriptions
purchases table to the subscriptions
purchases table to the subscriptions
table. I want you to pay close attention
table. I want you to pay close attention
table. I want you to pay close attention
to the subscription type column. At the
to the subscription type column. At the
to the subscription type column. At the
moment, if we expand the values inside
moment, if we expand the values inside
moment, if we expand the values inside
the column, we can see 0 2 and three.
the column, we can see 0 2 and three.
the column, we can see 0 2 and three.
These are ids that have a meaning to the
These are ids that have a meaning to the
These are ids that have a meaning to the
person who has designed the table. To
person who has designed the table. To
person who has designed the table. To
everybody else, it's completely unknown
everybody else, it's completely unknown
everybody else, it's completely unknown
what it means. To the end user, they are
what it means. To the end user, they are
what it means. To the end user, they are
just numbers. I mean, sure, we can tell
just numbers. I mean, sure, we can tell
just numbers. I mean, sure, we can tell
we have x many subscribers with an ID of
we have x many subscribers with an ID of
we have x many subscribers with an ID of
zero, y subscribers of an ID of two and
zero, y subscribers of an ID of two and
zero, y subscribers of an ID of two and
etc. But still, our end user will need
etc. But still, our end user will need
etc. But still, our end user will need
to have a dictionary in hand to separate
to have a dictionary in hand to separate
to have a dictionary in hand to separate
the ID zero, id two, and ID3. What do
the ID zero, id two, and ID3. What do
the ID zero, id two, and ID3. What do
they mean? So this is why we need a
they mean? So this is why we need a
they mean? So this is why we need a
better way to explain this. Therefore,
better way to explain this. Therefore,
better way to explain this. Therefore,
I'm going to ask Chad GPT what to do
I'm going to ask Chad GPT what to do
I'm going to ask Chad GPT what to do
about this. ID2 means annual, ID3 is
about this. ID2 means annual, ID3 is
about this. ID2 means annual, ID3 is
monthly, and zero is lifetime. We need a
monthly, and zero is lifetime. We need a
monthly, and zero is lifetime. We need a
solution from Chad GPT
solution from Chad GPT
solution from Chad GPT
ASAP. So I'm going to tell Chad GPT this
ASAP. So I'm going to tell Chad GPT this
ASAP. So I'm going to tell Chad GPT this
information and see what solution it
information and see what solution it
information and see what solution it
will provide for us.
So it is giving us two solutions. The
So it is giving us two solutions. The
So it is giving us two solutions. The
first one includes a calculated column.
first one includes a calculated column.
first one includes a calculated column.
The second one contains a measure.
The second one contains a measure.
The second one contains a measure.
measures are not suitable for working
measures are not suitable for working
measures are not suitable for working
with text values unless we're looking
with text values unless we're looking
with text values unless we're looking
for ways to generate some dynamic text
for ways to generate some dynamic text
for ways to generate some dynamic text
based on dashboard
based on dashboard
based on dashboard
interaction. This is not the case. So I
interaction. This is not the case. So I
interaction. This is not the case. So I
will use the calculated column option
will use the calculated column option
will use the calculated column option
and once we implement it, I will ask
and once we implement it, I will ask
and once we implement it, I will ask
Chad GPT to explain the difference
Chad GPT to explain the difference
Chad GPT to explain the difference
between calculated columns and measures.
between calculated columns and measures.
between calculated columns and measures.
First I will copy the code and I will go
First I will copy the code and I will go
First I will copy the code and I will go
back to PowerBI and paste it inside a
back to PowerBI and paste it inside a
back to PowerBI and paste it inside a
new calculated
column. Looks like there is a typo error
column. Looks like there is a typo error
column. Looks like there is a typo error
on the table name. We need subscriptions
on the table name. We need subscriptions
on the table name. We need subscriptions
table not subscription. I'm going to do
table not subscription. I'm going to do
table not subscription. I'm going to do
a very quick test to make sure that the
a very quick test to make sure that the
a very quick test to make sure that the
values are displayed correctly. We've
values are displayed correctly. We've
values are displayed correctly. We've
got annual, lifetime, and monthly. All
got annual, lifetime, and monthly. All
got annual, lifetime, and monthly. All
looks good. Okay. Uh now let's create a
looks good. Okay. Uh now let's create a
looks good. Okay. Uh now let's create a
slicer using the subscription type text
slicer using the subscription type text
slicer using the subscription type text
column we just created. I will select
column we just created. I will select
column we just created. I will select
the subscriptions type text. Then change
the subscriptions type text. Then change
the subscriptions type text. Then change
the visualization to a
the visualization to a
the visualization to a
slicer. I will resize it and move it
slicer. I will resize it and move it
slicer. I will resize it and move it
around next to the revenue versus
around next to the revenue versus
around next to the revenue versus
refunds visual.
refunds visual.
refunds visual.
Yep. Let's see if it works. H we are
Yep. Let's see if it works. H we are
Yep. Let's see if it works. H we are
selecting different subscription types
selecting different subscription types
selecting different subscription types
and the visual is not working.
and the visual is not working.
and the visual is not working.
It is a good general practice to always
It is a good general practice to always
It is a good general practice to always
test out whatever you do in PowerBI.
test out whatever you do in PowerBI.
test out whatever you do in PowerBI.
Never wait until you finish the
Never wait until you finish the
Never wait until you finish the
dashboard and then to start
dashboard and then to start
dashboard and then to start
testing. I know what the issue is and I
testing. I know what the issue is and I
testing. I know what the issue is and I
will explain it to you in the next
will explain it to you in the next
will explain it to you in the next
video. We're going to conclude this one
video. We're going to conclude this one
video. We're going to conclude this one
by asking a question to chat GPT to
by asking a question to chat GPT to
by asking a question to chat GPT to
explain the difference between measures
explain the difference between measures
explain the difference between measures
and calculated columns.
From this explanation, I want you to
From this explanation, I want you to
From this explanation, I want you to
remember two things about calculated
remember two things about calculated
remember two things about calculated
columns. Number one is that a calculated
columns. Number one is that a calculated
columns. Number one is that a calculated
column is something that gets calculated
column is something that gets calculated
column is something that gets calculated
rowby row for calculations on row level.
rowby row for calculations on row level.
rowby row for calculations on row level.
They're used for scenarios where you
They're used for scenarios where you
They're used for scenarios where you
want to create a new dimension or
want to create a new dimension or
want to create a new dimension or
attribute. Measures are used to
attribute. Measures are used to
attribute. Measures are used to
aggregate and summarize data across
aggregate and summarize data across
aggregate and summarize data across
multiple rows or groups of data. They're
multiple rows or groups of data. They're
multiple rows or groups of data. They're
dynamic and calculated on the fly unlike
dynamic and calculated on the fly unlike
dynamic and calculated on the fly unlike
calculated columns. Once you build the
calculated columns. Once you build the
calculated columns. Once you build the
calculated column, it remains inside the
calculated column, it remains inside the
calculated column, it remains inside the
data set as part of the table and takes
data set as part of the table and takes
data set as part of the table and takes
space and storage to keep it there.
space and storage to keep it there.
space and storage to keep it there.
Interestingly enough, at the end of the
Interestingly enough, at the end of the
Interestingly enough, at the end of the
answer, Chad GPD has provided us with an
answer, Chad GPD has provided us with an
answer, Chad GPD has provided us with an
explanation of our particular use case
explanation of our particular use case
explanation of our particular use case
scenario, which is pretty good. I have
scenario, which is pretty good. I have
scenario, which is pretty good. I have
to say I am surprised.
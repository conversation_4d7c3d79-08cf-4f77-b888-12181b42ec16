Video Title: 1_7_Formatting_Your_Chart
Video ID: F7IWJU73KFg
Language: English (en)
--------------------------------------------------

Kind: captions
Language: en
Do you find yourself struggling with
Do you find yourself struggling with
Do you find yourself struggling with
choosing the right colors? Maybe your
choosing the right colors? Maybe your
choosing the right colors? Maybe your
chart doesn't look quite right and
chart doesn't look quite right and
chart doesn't look quite right and
you're unsure on how to make it better.
you're unsure on how to make it better.
you're unsure on how to make it better.
Well, the good news is you can ask <PERSON>, the good news is you can ask <PERSON>
Well, the good news is you can ask Chad
GPT for some
GPT for some
GPT for some
assistance. Matte colors are quite
assistance. Matte colors are quite
assistance. Matte colors are quite
popular nowadays. But I am no designer.
popular nowadays. But I am no designer.
popular nowadays. But I am no designer.
I have no clue on how to make my own set
I have no clue on how to make my own set
I have no clue on how to make my own set
of matte colors. So, I'll ask <PERSON>t GPD
of matte colors. So, I'll ask Chat GPD
of matte colors. So, I'll ask Chat GPD
to generate some for me. for my net
to generate some for me. for my net
to generate some for me. for my net
revenue versus refunds chart. Can you
revenue versus refunds chart. Can you
revenue versus refunds chart. Can you
suggest some nice matte
suggest some nice matte
suggest some nice matte
colors colors for green and
colors colors for green and
colors colors for green and
red for the revenue versus refunds
chart? It has suggested several options.
chart? It has suggested several options.
chart? It has suggested several options.
However, we cannot see the colors, only
However, we cannot see the colors, only
However, we cannot see the colors, only
the hex code. Therefore, we're going to
the hex code. Therefore, we're going to
the hex code. Therefore, we're going to
test them out manually to see how
test them out manually to see how
test them out manually to see how
suitable they are. Remember you are the
suitable they are. Remember you are the
suitable they are. Remember you are the
designer here. Chad GPT is here to serve
designer here. Chad GPT is here to serve
designer here. Chad GPT is here to serve
you. Therefore, you can ask it as many
you. Therefore, you can ask it as many
you. Therefore, you can ask it as many
times as necessary until you got the
times as necessary until you got the
times as necessary until you got the
correct color plet. First, I am going to
correct color plet. First, I am going to
correct color plet. First, I am going to
highlight and copy the dark green color
highlight and copy the dark green color
highlight and copy the dark green color
and go back to
and go back to
and go back to
PowerBI. I will select the
PowerBI. I will select the
PowerBI. I will select the
chart, go to the formatting options, and
chart, go to the formatting options, and
chart, go to the formatting options, and
what we're looking for is to sort the
what we're looking for is to sort the
what we're looking for is to sort the
column color options. I know it may seem
column color options. I know it may seem
column color options. I know it may seem
a bit frustrating because Microsoft
a bit frustrating because Microsoft
a bit frustrating because Microsoft
likes to move things around. So if for
likes to move things around. So if for
likes to move things around. So if for
whatever reason the formatting option
whatever reason the formatting option
whatever reason the formatting option
does not look quite the same, it is
does not look quite the same, it is
does not look quite the same, it is
because Microsoft have shifted the
because Microsoft have shifted the
because Microsoft have shifted the
options again to different
options again to different
options again to different
menus. So now we have identified where
menus. So now we have identified where
menus. So now we have identified where
the color is and we're going to paste
the color is and we're going to paste
the color is and we're going to paste
the
the
the
green. I'm not quite sure about the dark
green. I'm not quite sure about the dark
green. I'm not quite sure about the dark
one, so let's try another green.
Okay, this one looks more lively. And I
Okay, this one looks more lively. And I
Okay, this one looks more lively. And I
will also pick up a
red. Okay, this looks good. Now,
red. Okay, this looks good. Now,
red. Okay, this looks good. Now,
finally, let's add a shadow to our chart
finally, let's add a shadow to our chart
finally, let's add a shadow to our chart
to make it stand
to make it stand
to make it stand
out. We have added the shadow, and let's
out. We have added the shadow, and let's
out. We have added the shadow, and let's
resize the date filter so it doesn't get
resize the date filter so it doesn't get
resize the date filter so it doesn't get
in the way.
in the way.
in the way.
This looks good. Next, let's fix the
This looks good. Next, let's fix the
This looks good. Next, let's fix the
title because at the moment it doesn't
title because at the moment it doesn't
title because at the moment it doesn't
look very
look very
look very
presentable. To do that, we need to go
presentable. To do that, we need to go
presentable. To do that, we need to go
back to the formatting visual options.
back to the formatting visual options.
back to the formatting visual options.
Switch over to the general tab and where
Switch over to the general tab and where
Switch over to the general tab and where
you see title, expand title, and this is
you see title, expand title, and this is
you see title, expand title, and this is
where you can edit the title of your
where you can edit the title of your
where you can edit the title of your
visual. So, we're going to name our
visual. So, we're going to name our
visual. So, we're going to name our
chart revenue versus refunds.
chart revenue versus refunds.
chart revenue versus refunds.
Okay, now looks perfect and our chart is
Okay, now looks perfect and our chart is
Okay, now looks perfect and our chart is
now looking a lot
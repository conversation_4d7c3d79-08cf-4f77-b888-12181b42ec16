Video Title: 1_6_Slicing_Your_Data
Video ID: OOQaI7e5QGU
Language: English (en)
--------------------------------------------------

Kind: captions
Language: en
Reports should be intuitive and easy to
Reports should be intuitive and easy to
Reports should be intuitive and easy to
navigate. We cannot have all the data
navigate. We cannot have all the data
navigate. We cannot have all the data
points displayed at our end users at all
points displayed at our end users at all
points displayed at our end users at all
times because this will confuse and
times because this will confuse and
times because this will confuse and
frustrate the user to the point where
frustrate the user to the point where
frustrate the user to the point where
they would not bother using the report.
they would not bother using the report.
they would not bother using the report.
After all, the idea behind each report
After all, the idea behind each report
After all, the idea behind each report
is to simplify data storytelling. Which
is to simplify data storytelling. Which
is to simplify data storytelling. Which
is why in this lesson, we're going to
is why in this lesson, we're going to
is why in this lesson, we're going to
learn how to filter or slice in PowerBI
learn how to filter or slice in PowerBI
learn how to filter or slice in PowerBI
terminology.
terminology.
terminology.
I'm going to ask chart GPT how to filter
I'm going to ask chart GPT how to filter
I'm going to ask chart GPT how to filter
our data and I will tell you why
our data and I will tell you why
our data and I will tell you why
filtering is not quite the correct
filtering is not quite the correct
filtering is not quite the correct
terminology when it comes to
terminology when it comes to
terminology when it comes to
PowerBI. So we want to have some sort of
PowerBI. So we want to have some sort of
PowerBI. So we want to have some sort of
a filter on the page that will allow us
a filter on the page that will allow us
a filter on the page that will allow us
to select any date range and our visual
to select any date range and our visual
to select any date range and our visual
should adjust accordingly. So you know
should adjust accordingly. So you know
should adjust accordingly. So you know
the drill. I'll go back to PowerBI and
the drill. I'll go back to PowerBI and
the drill. I'll go back to PowerBI and
ask the question. I will say the chart
ask the question. I will say the chart
ask the question. I will say the chart
is too big and has too many dates.
Is there any way I can filter my
Is there any way I can filter my
Is there any way I can filter my
data to a specific period in example
data to a specific period in example
data to a specific period in example
months between and
etc. In PowerBI terms, this means that
etc. In PowerBI terms, this means that
etc. In PowerBI terms, this means that
the filter will be applied in the
the filter will be applied in the
the filter will be applied in the
background and the end user will not
background and the end user will not
background and the end user will not
have direct access to the filter itself.
have direct access to the filter itself.
have direct access to the filter itself.
They will see the filtered data but only
They will see the filtered data but only
They will see the filtered data but only
to the specific date range that was
to the specific date range that was
to the specific date range that was
given by the report creator. We want to
given by the report creator. We want to
given by the report creator. We want to
give our users the ability to filter
give our users the ability to filter
give our users the ability to filter
their data as much as possible. What
their data as much as possible. What
their data as much as possible. What
PowerBI suggests is to add the filter
PowerBI suggests is to add the filter
PowerBI suggests is to add the filter
inside this filters menu. But this
inside this filters menu. But this
inside this filters menu. But this
filters menu is not always available to
filters menu is not always available to
filters menu is not always available to
the end user. It is also very difficult
the end user. It is also very difficult
the end user. It is also very difficult
to navigate especially when you want to
to navigate especially when you want to
to navigate especially when you want to
select a specific date range. This kind
select a specific date range. This kind
select a specific date range. This kind
of filter is not very useful to the end
of filter is not very useful to the end
of filter is not very useful to the end
user unless there is a specific
user unless there is a specific
user unless there is a specific
requirement for this report to be always
requirement for this report to be always
requirement for this report to be always
in a specific range. For example, to
in a specific range. For example, to
in a specific range. For example, to
show only 12 calendar months from now.
show only 12 calendar months from now.
show only 12 calendar months from now.
This is a suitable situation where we
This is a suitable situation where we
This is a suitable situation where we
may want to use this filter. For any
may want to use this filter. For any
may want to use this filter. For any
other purpose, we use something called
other purpose, we use something called
other purpose, we use something called
slicers. So let's ask for a slicing
slicers. So let's ask for a slicing
slicers. So let's ask for a slicing
solution chat
GBT and this time it is giving us the
GBT and this time it is giving us the
GBT and this time it is giving us the
correct answer. First we start off by
correct answer. First we start off by
correct answer. First we start off by
selecting the funnel visualization and
selecting the funnel visualization and
selecting the funnel visualization and
then we add the date. So we select the
then we add the date. So we select the
then we add the date. So we select the
slicer and we drag the purchase
date. Now we can freely select any date
date. Now we can freely select any date
date. Now we can freely select any date
period we want and our chart would
period we want and our chart would
period we want and our chart would
reflect on those changes. What I will do
reflect on those changes. What I will do
reflect on those changes. What I will do
next is to resize the visual and the
next is to resize the visual and the
next is to resize the visual and the
slicer to make some space for the other
slicer to make some space for the other
slicer to make some space for the other
charts and filters. This can be achieved
charts and filters. This can be achieved
charts and filters. This can be achieved
by dragging onto the corner of the
by dragging onto the corner of the
by dragging onto the corner of the
visual and resizing where
visual and resizing where
visual and resizing where
necessary. A good practice for
necessary. A good practice for
necessary. A good practice for
bookkeeping is to keep the filters in
bookkeeping is to keep the filters in
bookkeeping is to keep the filters in
one place. So all the filters will
one place. So all the filters will
one place. So all the filters will
reside onto the right hand side.
reside onto the right hand side.
reside onto the right hand side.
Perfect.
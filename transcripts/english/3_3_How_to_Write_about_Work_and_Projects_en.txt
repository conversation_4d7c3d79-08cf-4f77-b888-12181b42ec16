Video Title: 3_3_How_to_Write_about_Work_and_Projects
Video ID: PKh45T6-JNQ
Language: English (en)
--------------------------------------------------

When I look at résum<PERSON>, I see a huge
variance in how people talk about their
work. As a data scientist, you need to
be able to tell a great story using
data. I recommend taking this approach
when writing the descriptions of your
work and your past projects. When
talking about past job experience, if
possible, you want to make sure that
your work has three qualities. First, it
should create value. Second, it should
be quantifiable. And third, it should be
actionoriented. Let's pretend that in my
last job I built a model that helped
optimize growing conditions for a farm.
The traditional way of writing that
would be to say used XYZ model to
optimize growing conditions for a farm.
A data scientist description should be
something closer to increased crop yield
by 12% over the 6-month growing season
through the integration of XYZ model.
The focus here is on the outcomes and
less on the specific model used. If
they're interested in the outcomes,
they'll naturally ask about the models
that you used during the interview
process. I like to use the formula
action verb plus quantitative outcome
plus method. I found that this approach
resonates best with recruiters. Of
course, not everything is quantifiable,
but I recommend trying to do your best
with your experiences. This goes for
your projects as well. You should start
with the outcomes of the project at hand
and after that you can talk some more
about the technical aspects in the
following bullets. Let's do a quick
example of one of the projects on my
YouTube channel. In this project I
estimated glass door data science
salaries from data that I scraped on
their website. As you can see in the
first bullet I talk about why I did the
analysis and the value that it created.
Next, I talk about some of the specific
technical elements that we go through
and that I used. In this case, a
recruiter can see the value that was
created right away. And if they want to
learn more about the tools that were
used, they can keep reading. You can
also link to the specific GitHub repo of
their project. So, if a recruiter or a
manager wants to learn more, they can go
in and look at the code. Now that you
understand how to write about your work,
in the next video I'll talk about the
importance of customizing a resume for
each position that you apply
Video Title: 8_7_Glassdoor_Findings
Video ID: 9iBPKadNZKE
Language: English (en)
--------------------------------------------------

Hello everyone. My name is <PERSON>. On my
YouTube channel, I recently did a new
project from scratch series where I went
through and analyzed 1,000 data science
glass door job postings. I thought it
would be fun and informative to share my
findings with the greater 365 data
science community. Although the search
term was data scientist, my scraper
pulled in data for data analyst, machine
learning engineer, data engineer, and
manager level positions. This allows us
to learn some interesting things about
the analytics field as a whole. First,
let's look at where these positions are
being hired. As you would expect,
California leads this category with a
little more than 20% of the total job
postings. Other states with the largest
cities in the country also top this
list, like Massachusetts, New York, and
Illinois. If you're looking for a data
science position, your best bet is
somewhere near one of the larger cities.
Only about 25% of the jobs are located
outside of the top 10 states on this
list. Next, let's look at who's hiring.
From this sample, it looks like
medium-sized companies are hiring the
most data scientists. Private companies
are also taking the lead here with
almost half of all data science jobs
being hired by private organizations.
Companies in information technology,
biotech, business services, insurance,
healthcare, and finance top the list of
the most popular jobs by sector. If you
have a hybrid skill set where you have
experience with one of these domains, it
could greatly help you create an
opportunity for yourself. It should be
noted that these can be slightly skewed
by the type of companies that would post
their jobs online. Small companies are
likely under represented here because
they're less likely to post their jobs
on websites like glass door or LinkedIn.
In terms of programming skills, Python
and SQL are independently mentioned in
over 50% of the job postings. R is also
in high demand being asked for about 30%
of the time. Scola and Java are also
mentioned around 20% of the time. For
data manipulation tools, Excel is still
king. It's mentioned in just over 50% of
the total job postings and in 49% of the
data science specific job postings.
Honestly, this was pretty surprising to
me. Next is Tableau at 20%, PowerBI at
8% and Click at 3%. For cloud computing,
Spark and AWS were both mentioned around
20% of the time. I also looked at the
educational requirements for these jobs.
A whopping 95% mentioned a master's
degree and 27% mentioned a PhD. Don't
worry, these are usually part of the
preferred requirements for the job,
which most people do not meet. The last
thing that I looked at was the expected
salaries across the job types and
locations. From the estimates on glass
door, it looks like directors make the
most, followed by machine learning
engineers and then data scientists. What
was surprising to me was that the
managers were expected to make
significantly less than the data
scientists and machine learning
engineers they are managing. I next
looked at data science salary by state.
It appears that Washington DC and
California lead this list. What caught
my eye was that New York and
Massachusetts offered significantly
lower salaries even though they have a
very high cost of living. Perhaps if
you're looking to get the most bang for
your buck, you would consider positions
in Utah, Missouri, DC, or Illinois. I
hope that these insights have helped you
to make more informed decisions along
your data science journey. If you want
to learn more about this analysis or the
data science lifestyle, feel free to
check out my YouTube channel linked
above and in the description below.
Thank you so much for watching and great
to meet you
Video Title: 1_3_Types_of_Data_Science_Roles
Video ID: 0Qkne9O9T6w
Language: English (en)
--------------------------------------------------

Many people are calling data science the
sexiest job of the 21st century.
According to LinkedIn, data science jobs
have experienced a 37% hiring growth
over the last 3 years. Data science is
also quite profitable. On average, data
scientists are making around
$115,000 per year in the US. There's a
high demand for these skills, but
there's also an extreme amount of
competition. Before going out and
applying for jobs, it's important to
understand what types of opportunities
are out there in data science and in the
broader data science field. Data science
positions vary greatly by company and by
industry. Within the broader umbrella of
data science, there are also
subcategories. You can also work as a
data analyst, a data engineer, a machine
learning engineer, or just a a general
data scientist. Remember the data
science life cycle that I showed you in
the previous video? Data engineers
usually focus on the data collection and
the data cleaning steps. Data analysts
generally focus on the data cleaning and
the exploratory analysis. Finally,
machine learning engineers focus
primarily on the model building and
production steps. Someone with the
broader data science title can work
across this whole spectrum. These are
not set in stone as the expectations for
the work may vary greatly by company.
Across these different roles, you also
use very different tools. Refer to the
tool breakdown by role in the
supplementary content of section 8 for
more information on this. These are all
viable roles and this course will
prepare you for a career in any of them.
When thinking about your next job, you
should also think about the size of the
company and the industry that you want
to work in. A data science job in a
large company can be vastly different
than the same job in a startup. I found
that in smaller companies, you have an
opportunity to touch more areas of the
business, and in larger ones, there's a
greater opportunity to specialize in a
single area. This doesn't hold true for
all companies, but it's a good rule of
thumb. When you're looking across
different industries, subject area
expertise can be extremely important. If
you have a pre-existing knowledge about
an area, it can greatly improve your
chances of landing that role. For
example, if I'm an experienced gardener
in my free time and I understand how
crop yields work, that could help me in
my interview process for working with a
large agriculture company. Finally, when
thinking about the positions you're
applying for, you should evaluate your
own experience and your capabilities. If
you haven't had previous experience as a
data scientist, it might make more sense
to shoot for an entry-level data analyst
role and work your way up. If you don't
enjoy working with SQL and data
cleaning, but you have a very strong
software engineering background, you
might be inclined or have an affinity
towards a machine learning engineer
role. Now that you have an understanding
of the types of roles associated with
data science, in the next video I'll
introduce you to the interview process.
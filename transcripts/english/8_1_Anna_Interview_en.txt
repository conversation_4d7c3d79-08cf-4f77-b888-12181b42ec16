Video Title: 8_1_Anna_Interview
Video ID: FhlY0GQwM2w
Language: English (en)
--------------------------------------------------

Awesome. Well, hello everyone. Welcome
back to another interview that I've done
for my course, How to Start a Career in
Data Science. Today I'm talking to <PERSON>,
who has recently interviewed for quite a
few data science positions and
eventually landed one. So, I think she
has a very interesting perspective on uh
tips to help you improve your chances.
She also comes from a PhD background in
civil and environmental engineering. So,
that's a different perspective than
anyone that we've seen so far. Hi, <PERSON>.
Thank you again for coming on. Uh do you
have anything to add that I might have
missed in your background there? Um
well, thanks for having me. Um I think
that's pretty much all. Um I'm currently
working um at a digital marketing
company um in Chicago area. Very cool.
Very cool. And so, you know, I I'd love
to hear kind of first about your
interview process and your job search.
So um you know roughly you know if
you're comfortable how many companies
you applied for and what that interview
process is like you know were there a
lot of phone screens were there
technical questions uh where did that
all fall? Uhhuh. Sure.
Um so I can't remember a specific number
and um I actually don't recommend you
know not tracking the jobs you apply. Um
that's good advice right there. Make
sure you track all of them. Here we go.
Um but I think um I applied a lot most
of them you know I just apply either um
via the company's websites or LinkedIn
um or glass
door um and um I think I ended up
probably getting actually less than 10
um interviews asks starting from the
phone interview. So, you know, that's
really not a um huge turnout rate. And I
can get back to this later on, but
um I'm sorry, did you ask um what the
interview process was? Yeah. So, you
know, did most companies have like a
phone screen and then, you know, was
there a take-home test or, you know, was
there like a technical assessment? What
did you see most commonly in those
processes? Uhhuh. So all the positions I
applied were data scientist roles and um
there were lots of
differentiations. So I think most
frequently I got the first round a phone
screening by some HR person. um never
gets into any technical details but just
you know ask about your general
backgrounds and also very importantly
describe for you the company's basic
information just to make sure you know
everything is aligned and um that can
actually um fill rapidly because
sometimes um there is a visa sponsorship
issue for international students. So um
I think most of the failures I knew at
this point were actually um the visa
visa sponsorship issue. Um but after
that um it actually gets um a lot
smoother most of the times because the
second round is usually a technical
interview um where I think it's almost
always a um hiring manager or a you know
future maybe collaborator also a data
scientistish um who will dig into your
technical backgrounds asking you about
um the projects you've worked on, your
you know basically anything about your
technical background um and sometimes I
also had some
um like code exams via online platform
as the second round and then followed by
that inerson technical interview. Um but
you know usually the second round is a
technical interview with the hirer
manager and after that if you move
forward there sometimes can be another
round of technical session that can be a
take-home project um and the pro well I
think the projects can usually vary from
company to company but I think most
frequently what happened is um asking
you to solve a data science this problem
from end to end by providing you some
proprietary data and then ask you to
turn basically everything back and
sometimes also involved a presentation
of your findings. Awesome. That project
uh yeah go ahead. That project can also
range between like a couple of hours all
the way to seven days as a deadline. And
after our projects or a technical
interview um usually that can leads to a
final day um at the company um which is
a full day back and back meetings um
with all the potential collabor or
potential colleagues you'll be working
with and also again with a hire manager.
Yeah. Well, you know, I think that one
thing that is very apparent and from a
lot of the people that I've spoken to is
that the the data science interview
process is not set in stone. You know,
it's very different depending on the
company you go to. You know, I had a
company that when I was applying quite
some time ago, they asked me to come in
and basically present one of the
projects that I've done at another
company that I was allowed to share. So,
I had to come in and build a PowerPoint
deck. I had to walk through all the
logic that I used. And you know the best
thing that anyone can do to prepare is
like really work on your own projects.
Make sure you understand the data
science life cycle. Um but you know also
do your research on the company on glass
door to make sure that you can at least
try and figure out what to expect uh
when the interviews uh start coming in.
So thank you so much that I think that
that was an awesome answer. I think that
that alluded to how uh how broad this
this field can be when interviewing. Um,
I would like to to better understand how
you landed uh the job that you ended up
in. You know, was it was it traditional
through the interview process? Was there
a referral involved? What did what did
that look like? There was a referral
involved. And um I think a second tip I
would love to share is to be really
proactive when seeking opportunities.
Um at the beginning of my experience I
thought I was proactive enough but then
um several months later I I I thought
you know I I really didn't really seek
out um so in the end um what I did on
daily basis was reach out to people whom
I either knew or whom I you know never
knew before. Um that could be some code
messages via alumni um association or
platform or that could also be just code
messages uh via LinkedIn um to people
from the company you're interested in
and even though um of course most of the
people you know won't get back to you
but um there's still a positive
opportunity there and you will always
hear back. Yeah and I I couldn't agree
more. I I think that it's so important
to make personal connections if you can.
Um whether it's with someone at the the
company for anformational interview,
whether it's through, you know, through
a friend, you you never know where where
those things could lead. You know, they
might not necessarily end up in a job,
but they might end up in in like a
friendship. You never know, right? Um,
and you know, people who are your
friends are are a lot likely a lot more
likely to lobby for you in a position.
You know, if if um if they see something
open up and they know that you're
looking, there's a lot higher chance
that they'll actually go ahead and and
refer you for for a position that's not
even inside of their company. So, that's
so true. Yeah.
Yeah. Go ahead. Go ahead. Sorry. Um also
you know just completely e echoing what
you just said and also um another
benefits of reaching out is you can know
in advance whether there's a good fit.
So you won't waste much time um you know
from either side either your side just
waste a lot of time to tailor your you
know resume and cover letter and
reaching out to a bunch of other people
or the other side from the hiring team
right because if there's no match then
just don't your waste your time doing
so. Well, you know, that's 100% that's
something that I actually um I don't
touch on in the course, and that's time
management. I mean, there's so many
different ways you can spend your time.
You can spend it all just sending out
applications. You can spend it reaching
out via via email or LinkedIn. Um you
know, you can spend it, you know,
working on projects. And I think what
what you're addressing is it's really
important. You don't want to waste time.
You know, there are some activities that
you can do that are a lot more efficient
with your time than others. you know,
rather than applying to a zillion jobs,
actually reaching out to people might be
a better use of your time in general.
So, that's something, thank you for that
insight. I I I I overlooked that when I
was putting the course together. So,
that's Sure. And that's a great summary
what you said. Um, you know,
so as you've worked in data science now,
what what do you think the most
important skill um is to have in the
field?
There are a few um I would say most
important is problem solving. Um and you
can also see immediately um here the PhD
background also and taking an effect
here. Um I think what I learned most
from my PhD experience and also I'm I'm
still applying on daily basis is really
problem solving skills. um that can be
you know given a broad ask try to
decompose the ask um figure out a plan
to come up with the results and also
learning on the go um because usually
you haven't you don't have the
background yet to solve that problem and
also collaborating with different people
um in tackling a usually interdisipline
iplinary project and also communicating
um tailored to people from different
backgrounds all the way to the you know
the technical coding um involving the
you know um data cleaning machine
learning you know and also interacting
with databases all the way to some
visualization and so forth um so I hope
that answers the question no that that
was an awesome answer and you know I'd
actually like to bring that answer back
to the interview process you know
Getting a job is a problem-solving
exercise. You know, you have to
experiment. If you don't have success
with one company, like you should always
be looking at, hey, what what went
wrong? Can I ask for feedback? How do I
improve to go into the next interview
and to put my best foot forward? You
know, if I'm applying through all the,
you know, the traditional routes and I'm
not getting success, you know, maybe I
should start reaching out to recruiters
via via email, trying some of these
other avenues. So I think that like the
the data science uh the the skill that's
that's relevant for the field is also
something that you can work on while
you're even getting trying to get the
first job in the first place. So I I I
really like that answer of yours because
it applies to the whole process, not
just on the job work, but even before
you get the job work. So that was really
awesome. Thank you. Um, you know, the
the last thing I'd like to add on, you
know, because we've kind of gotten
through pretty much all of the interview
related stuff, um, I'd really like to
know what type of tools you use on the
job, what types of of, uh, problems that
you're comfortable sharing that that
you're helping to to solve.
Okay. Um, I would start with the tools I
um, I'm using on daily basis.
Um, SQL databases.
Um of course and also um we use spark to
deal with big data. Um we also use
machine learning on databases of course
um you know to leverage information from
the big data.
Um
see I think well um oh also importantly
the um coding platform or the language
uh we use are both Scala and Python.
Scala very cool. Yeah I think that's
pretty much it. Very cool. You know, I
think that that's something that uh is
is very interesting because almost
everyone I ask this question to, they
almost always start with SQL, right? And
and the thing is that everyone gets so
excited about, oh, deep learning. Uh you
know, I have to know TensorFlow 2.0
since it just came out.
Um only a segment of data scientists are
using deep learning all the time. if you
have really solid skills with SQL,
Python, and the traditional machine
learning libraries, unless the role
calls for very specifically um some of
like the Hadoop and and big data
frameworks, um you can go really
far just doing great with the
fundamentals. So, you know, you
mentioned that the first thing out of
your mouth wasn't deep learning, right?
And that's like that's something that
that people lose sight of uh in this
whole process, including the
interviewers, I think.
True. Very cool. Uh I think that those
are generally all the questions I have.
Do you have anything you want to add?
Any any last piece of advice for for
anyone watching this or or searching
through this process? Um
sure. Um I think what I learned from my
job searching experience a year ago um
was don't get frustrated too early. Um
there were a lot of frustrations
especially the first time seeking for um
job. Um but you know it it just occurs
so so universally I would say um
especially if you um have a
international you know visa status um
it's just so um easy that um you get
rejected just because of the visa
sponsorship issue. Um so don't get so
frustrated if you feel that you already
sent tens of application and never heard
back. That occurred to me so you're not
alone. Um and also um it's like a
gradual process um because after a
couple of interviews you will start to
learn um what the interviewers are
really uh wanting to hear about.
Um the first couple of interview were
like a guess game. Um because you just
know so much information you really
don't know you know what you really want
to want me to say but after some
experience you know exactly what to say
and then um I think that's the point
when you will see a gradual um you know
yield from um from your interviews. Well
that's awesome. I mean it's just like
data science like the more you do it the
more comfortable you get uh the more
kind of pattern matching that you have
the more intuition you build around it
and so um you know a lot of people will
ask you know am I ready to start
interviewing and I almost always say
like how are you going to know if you
don't do a couple interviews right like
that's that's part of the learning
process I mean granted you might bomb
one or two but like the experience and
the and what you learn from going
through those interviews is is so so
valuable um to actually improving your
chances going forward that it's okay to
to feel a little embarrassed, feel the
heat because if you're if you go in and
you feel like you're not prepared, like
that's going to be a lot more of an
intense environment that down the road
if you feel like you are more prepared,
you're not going to have to worry about.
So Anna, thank you so much again. This
was really really awesome. Uh I greatly
greatly appreciate uh you coming in and
chatting with
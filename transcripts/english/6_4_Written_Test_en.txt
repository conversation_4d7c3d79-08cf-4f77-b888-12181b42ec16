Video Title: 6_4_Written_Test
Video ID: 3Ig8iF9xIdQ
Language: English (en)
--------------------------------------------------

The third and final type of assessment
that I've seen is a test with a few
short answer math and statistics
questions on it. I'm not sure why these
really are sent out because I think
there's a high chance that someone just
searches the internet for the answers.
Still, I don't think that that's really
ethical if you're in that
situation. I would prepare for this test
very similarly to a technical interview,
which I'll discuss in the next section.
First, you should brush up on statistics
and model building
questions. It's important to understand
the math behind any of the algorithms
that you use at a high level. You should
also have a good grasp of statistics,
linear algebra, or calculus to prepare
for any type of question you might be
asked here. Going through the data
science interview questions in the bonus
feature section is a solid way to
practice these. Now that you have a good
understanding of the take-home test
criteria, in the next section, I'll show
you how to put your best foot forward
for the in-person
Video Title: 8_6_Sheng_Interview
Video ID: th9K3CltfYc
Language: English (en)
--------------------------------------------------

Awesome. So, hello everyone. Welcome to
another interview for the how to start a
career in data science course. I'm
interviewing today <PERSON><PERSON> who has a
really interesting background at a
Fortune 100 company in as a data
scientist. He also is interviewing data
scientists on a fairly regular basis.
So, he can provide some really cool
insights both for how you should prepare
for the interview and what he sees on
the other side of the table. U you know,
Shang I've actually I've worked with
him. He's he's an awesome data scientist
and he's he's very on the on the
entrepreneurial side. So I think he'll
give us some some unique perspectives
here. He also uh is was an international
student when he was applying for data
science jobs. So that perspective will
also be valuable for for anyone coming
uh from outside the US trying to get a
job in the US. Chang, how are you doing?
Thank you so much again for for coming
on and chatting with me. Is there is
there anything I missed? Anything you'd
like to add there? Sure. Sure. Uh it's
great. uh uh talk about these
um experiences that I have before.
Hopefully this is helpful. I'm doing
great. Uh hopefully you have also been
uh keeping yourself busy interviewing
other folks. This is great. Uh thank you
for having me here. So I think a couple
of things I'd like to add is that I've
been working with my current company for
about uh four to five years and as <PERSON>
you mentioned I started as a
international student and I actually
started my summer intern with the same
team and later on I I got a return offer
and I started the full-time job after I
graduated.
Awesome. So we can cover more of that
when we go to the specific questions.
Awesome. So, you know, I I think um one
thing I I'd like to ask before before we
really get into the meat is how you got
kind of in introduced to data science
and how you you know found it first
found it interesting, how you heard
about it, those types of things.
So I had my undergrad back in China in
engineering and uh I worked uh in
electric utility for a little bit about
uh two and a half years before I came to
US to get my graduate study. And during
my first uh
career uh I was working with a fairly
large uh Oracle data base with a lot of
uh customer or
enduser information like account, their
um purchase history,
uh how they have been paying for their
electricity bill, things like that. and
that at that time uh I was not working
heavily on the data set on data side but
that's something I always feel uh
interesting and when I came to US I
started my master degree in engineering
management uh hopefully to learn more
about the man material side as a
engineer and during that program I
participated in a competition that was
sponsored by Accenture and Microsoft. Uh
so that competition really triggered my
passion uh and the follow uh the
following path that I
took from which I became a data
scientist. That's awesome. I actually I
didn't know that story. That's really
cool. Oh, cool. So that that case
competition was very really open uh
ended. We got a bunch of
data but there is not a specific uh
question. The data is about Wi-Fi usage
in a office environment and uh what the
teams were to analyze the data and to
present can be any findings any
recommendations and in fact the team I
worked with we got the first place among
I think uh 10 or so teams. So at that
time I was looking at uh a another
master program that was kind of next
door because it's under the same
department at Northwestern University.
So after I finished my first master
engineering management, I went directly
to the second master degree which is
master in analytics.
So that's really the path I started had
a little uh interaction in for
uh the job that I work about the
database. Um and then because of that
competition I really wanted to get into
the details and I took my second master
degree. Awesome. Well, you know that's
something I really recommend to a lot of
people is getting into hackathons,
getting into these competitions. one
they are great networking opportunities
for employers but two they give you
something really cool to show for
yourself when you are applying. So, you
know, that kind of brings us into any
any interviewing advice, any things that
you think would really help these data
science aspirants. Do you know what
would what would be your best piece of
advice for anyone who's trying to get
into the field here?
I think first uh
um of the advice that I have on top of
my head is really to make sure as a
interviewee
uh the basic concept is always there um
with uh the questions coming
up. you should be able to quickly answer
and uh clearly describe the concepts
especially uh when the question is
about the projects that you have worked
I think it's important to be able to say
this is what type of analysis this is
supervised learning or unsupervised
learning or um what uh technique uh has
been
used. So I think uh if there's any
hassidation or if uh you're not able to
clearly describe those aspects, it will
raise the question to the interviewer
thinking if you really worked on those
projects or if you are really familiar
with those basic concepts. Awesome.
Well, that's definitely something that I
recommend in the course is like know
your own stuff cold. you know, when
before you go into an interview, before
you do a phone interview, make sure to
review your own resume and make sure
that you can really talk about all these
projects that you've done intelligently.
So, I think that that's awesome advice.
Thank you so much. And sure, you know,
we've talked about how you are from an
international background. How is that
interviewing process different than
someone who is, you know, a USB born
citizen like myself?
I think this is actually related to uh
the first question. as an international
student, I do have this experience that
I um I was really I think uh trying to
grab any opportunity that that I could
potentially get an interview or even to
talk with a company. Uh we all know that
there's some restriction as
international students. uh there are uh
companies that they prefer not to hire
any international background students
out from school and there have
legitimate reasons. Um and uh that is I
think something we need to as
international students or as
international students we need to keep
in mind that don't uh feel that uh all
the companies are going to like that.
That's not true. There will be companies
um that are still willing to hire
international students. uh there is I
think uh a little bit extra homework to
do and uh when
uh you as international student hear
that this company does not sponsor
whatever work visa or they do not even
want to talk with you just move on don't
feel that uh it's the end of the world
and I think um it's also important as
international student to get more
connections to get referrals
Even with those companies that they say
they do not hire international students,
if you have a v a way to connect with
that, for example, the hiring manager or
people who um are currently working
there, you still have some chance to get
in there. That's actually my own story.
I started working with this company and
uh uh they do not hire international
student by
uh whatever they say on the job
description that's always there. They
say they do not hire international
students. But because of my manager who
hired me from the first place, he was
building uh basically a new analytics
team, hiring new data scientists from
scratch. he had the uh
flexibility and he had a special
arrangement actually for me to get on
his team through a staff agency. So I
started as a contractor employee which
does not uh
uh which was not against the company's
policy that they don't hire
international and after a
while I got my work visa
uh through that stuff in agency and uh
the company uh they they saw this as
less risky they just hire me directly.
So I became a formal employee within uh
about a year since I did in my
internship. That's awesome. So how did
you get hooked on with the staff a
staffing agency again?
So I think in my case I did not do much.
It was the hiring manager because he is
building the team and he really liked
me. So he basically took care of all the
paperwork. it it's a I think it's a work
order from the company to the staffing
agency and they just tell the staffing
agency hire this people uh for us just
uh have the employment uh with him and
we
will get this person from you guys so uh
it's not against the company's u
regulation say they don't hire
international wow well that's you know
the ultimate power of referrals or
networking there is that, you know, you
can get into some of these places that
seem like a completely closed door
through the power of personal connection
because, you know, there's always side
doors. There's always um you know, I I
read this book about um it's called the
third door, right? And so the f first
door is the front door, right? Yes. You
know, there's usually if you're talking
about like a club, there's a there's a
fee to get in the front door, right?
Yeah. And then the second door is the
VIP entrance, right? Right. and you have
to have special credentials to get in
the VIP entrance. But the third door is
you go in through the kitchen or
something and you just have to know
someone on the inside for them to let
you, right? And so I think that that
idea, that concept is very powerful. And
I think that if there's anything that I
want to drive home with this course is
that like, you know, it's not the
strength of your resume necessarily.
It's not the strength of your projects,
which can be a tremendous help, but it's
how you leverage those and communicate
with people to get them to get kind of
on your side in the hiring process. So,
right, you you know, we we talked about
how it can be really difficult. How many
different companies did you apply for,
for example, um to to try and get your
first role?
So, it may uh be a little bit
surprising. I did not interview many
companies. That's uh as far as I
remember it was the number of companies
would not
uh many more than just a handful. I
think uh as you mentioned Ken just now I
I probably went through the third door
and act that actually is from my
classmate at that time because she got
uh I think a internship offer
from later my manager but she turned
down that offer and uh she was saying
okay I'll look for if there's any other
people in my class who will be
interested in this job and She also
mentioned my name to to that manager and
by the time I talked with him he already
know my name. So I think uh again this
is uh not to say it's leftover. Uh it's
going to be I think uh individual
preference because that internship is
not uh I think a good fit for my
classmates but that act it was actually
something that I was very interested in.
So I think it turned out uh pretty good
for the hiring manager and also that's a
uh I think a opportunity that uh um
saved a lot of my time to interview with
other companies. Yeah. Well, you know,
another thing that I that I stressed in
the course is how to use like
informational interviews, right? And
although it's not the exact same where
you know your your friend had this
opportunity that that she knew about
when you go into informational
interviews you're not asking for a job.
You're you're just getting more
information about data science and that
person's career. And a lot of the times
they'll say oh I know of an opening over
at this other company once they get to
know you a little bit even if there
isn't one available at your company. So,
you know, you've used two perfect out of
the box examples of how like making good
connections can really really be
valuable in your career. And I I think
that that is so awesome. You know, I've
interviewed probably three or four
people so far and almost all of them
have not gotten a job through like the
traditional apply application process.
Um, and you know, I I chose a pretty
random random sample of my friends or
people that I thought would, you know,
have really good stories to tell.
That is that is so awesome, Chang. Um,
you know, one thing we kind of alluded
to before is like the the project work
you did or the um, you know, the the
like competition experience or like the
the hackathon type experience you've
had. Um, you know, what what did your
projects look like um, either in school
or or throughout that process when you
were applying?
So that was actually a number of years
ago uh when I was interviewing for this
internship and later became a full-time
job and at that time most of my projects
were school related. They cover a fairly
wide area from
uh unsupervised learning to supervised
learning. uh we had projects about uh
predicting if a potential donor will
make the donation and how much that
donor will actually give. So that was
one project. I remember uh there's
another project about uh predicting
churn and uh I was also working with uh
an outside company that that was part of
our curriculum. It's called the
practicum project. We actually have a
team of graduate students. we uh work as
graduate students, consultants with
companies and solving real world uh
business problems. And in that case, it
was a customer segmentation uh project
that I was working on. And at the time
uh when I was going for my interview for
this internship, I had actually
um quite a few projects that can cover
different aspects in terms of analytics
and data science. Plus, I also had uh
other uh work that I've done uh both
related to the uh class assignment work
and also something I I I do myself
outside of a classroom like uh helping
uh some faculty member at the university
to do research work and I was helping
wrangling their the data set and also I
did
um data visualization with like
dashboarding Uh I remember that was
Tableau that I did something in Tableau
and I uploaded in the T table Tableau
public. Uh I forgot the name of the
website but that there's a place that
you can yeah Tableau public. There's a
place that you can actually upload the
dashboard the visualization you you made
and people can can play with it. So
that's very powerful I think. Awesome.
Well I I think the project that you did
real consulting work for for an actual
company is is so incredible. That's
something that you know if I was hiring
data scientists I'd know that they had
real real world experience with a data
set and I I have to imagine that that
took you really far. Um I think it is
important to note that you know you were
doing that through school but for anyone
else out there watching they don't
necessarily need to go through school to
do a project like that. I mean in theory
you guys were doing it for free right?
Yes. You know most people won't turn
down free labor like that. uh even if
you're more of a beginning data
scientist. So you can look at at
companies in your area. You can ask your
friends, connections, whoever it might
be. And you know, there might be
opportunities to do some free work.
That's a like a really good project for
your resume. Um I I I personally would
first look to like nonprofits in your
area. It's a great way to give back. Um
and that also can show that you're a
little bit altruistic on your resume.
So, let's move on a little bit to uh to
like your your personal work and like
when you're interviewing what you look
for. So, I'd love to know first about um
you know like what tools you're using on
the job mostly and also about kind of
how your team is structured. I think
that one thing I don't go into too much
in in this process is how like the the
teams work together. So, that could be
some pretty cool insights for a lot of
people.
So
currently what I use most is pi uh not
python. I do use python from time to
time but r is my I think number one uh
programming language in terms of
analytics and data science and I use a
lot of SQL and uh uh I do a lot of stuff
still with Tableau and within the
company a lot of people they still like
using Excel. So, Excel also takes part
of my time. So, those things adding
together uh are the tools that I use to
do the analysis to present visualization
and story and insights to other people
in the company and also to help them get
access to the data uh in the way that
they like which is in Excel form. Very
cool. Very cool. And so, how many how
many people are on your team? What does
that structure look like? you know, do
you work mostly uh autonomously most of
the day or do you work with a team most
of the day? What what does your kind of
workflow look like? Um I would go from I
think a more general uh aspect from the
broader team rather than my immediate
peers. Of course, uh it's in I think
it's important to have the uh
distinguished roles working in any
company as a data science or analytics
to differentiate whether you are doing
uh the data work as a data engineer or
you are doing the analytics work as a
data analyst or data scientist because
these can be very different. In my case,
I have been mostly working on analyzing
the data, doing a lot of data,
manipulation, wrangling and building
models and find uh insights to and
present those insights. But I uh I I I
will give a lot of
credit to the data engineering team in
our company. They've been doing a really
really good job. And also um we have I
think this happens in a lot of big
companies. We have a lot of different
data warehouses. We have Oracle. We have
uh Microsoft uh uh SQL data warehouse.
We have Hive. We have SAS.
um we had the green plum before uh and
and we use uh different data sources. So
in that case it's important to I think
build a good relationship with others
especially uh the data engineering team
as well as the business functions or the
subject matter experts because they know
what's really going on in the in the in
the business side and the data
engineering team they will know okay
what are the table what are the schema
and database um and uh what actual
business activities they are related to
so I think it's important to uh to be
clear and uh to know the people that um
as a data scientist uh will rely or
collaborate the most.
Very cool. Very cool. Well, now let's I
just want to ask a couple questions
about you know when you're interviewing
um candidates for your team. Um so what
is something that a candidate does that
you know we talked about this a little
bit but something that a candidate does
that turns you off? So you talked about
the hesitation not knowing their their
own stuff. Is there anything else that
that you're like that's a strike against
them if they do this? I think one
example I remember that immediately
turned me off. It still goes back to the
basics. I was having lunch with this
candidate. Uh of course he was having a
full day interview and I was only one of
the people who was uh interviewing him.
And that lunch that he had with me was
actually the interview that I I was
having with him. I don't know if he was
maybe a little bit relaxed or he he did
not realize that. But during that launch
um I was asking can you talk a little
bit about the difference between
supervised learning and unsupervised
learning and he could not articulate the
differences. So that was immediate
turnoff. Let's see. So I think you know
that's a question that almost I I'd say
a lot of uh a lot of interviewers ask
you and it's a very basic question. It's
something that like probably even like a
data analyst should know not only a data
scientist. So um you know you might know
all of you might know uh might be able
to implement some really cool deep
learning algorithms but if you don't
understand the very basics of what
supervised versus unsupervised learning
is uh that's definitely something you
should absolutely know stone cold
because I mean you're probably mostly
going to be super doing supervised
learning but like there's only literally
like two and a half types of different
learning in in in data science. So you
have supervised, semi-supervised, and
unsupervised. So um you know on on the
flip side of that, you know, I'd say
that that's more of a technical
question, but what do you generally look
for in the technical interview?
Um it's I think from my uh experience I
had interviews with candidates who are
applying our summer internship positions
as well as our full-time
positions to get to know or to get a
better sense of what their technical
capability or experience. Uh it's always
good to start asking the projects that
they have worked on rather than just the
concepts. the concepts is the basics. I
think uh uh just take one step back
during the interview anything related to
technical when the candidate does not
make mistake or uh does not miss any
basic or important things that's a green
light but there's if there are holes
then we need to see if that is a big
problem or if that is a minor problem.
So to answer this question um doing a
technical interview um what I uh
value would be if the candidate can
explain why he used that modeling
technique for that project. I'm talking
about he or she why he or she used that
technique for the project he or she did.
And I think uh that's a question um I
can from which I can understand if this
candidate really understand that
technique itself and also how to use it.
Awesome. Well, I think a very concrete
example of this is when you're looking
at like I I like to ask about oh okay
how do you what are the different ways
to handle missing data for example right
so you can either just remove the rows
or you can uh imputee it with like the
median or the mean or you could do some
sort of regression that that fills it
in. Um, but the the reason why you make
one of those decisions has a lot of
intricacy, right? You know, maybe
there's very few missing values and it's
like, okay, the ones that are missing in
one column, um, they're missing multiple
columns. We'll just remove them, right?
Um, but a lot of people will freeze and
say, "Oh, I just that's the way I do
it." And, you know, that's not how you
can answer that question. You really
need uh the why there. So, exactly. I I
I think that that's that's really uh
really insightful there and hopefully
people will will take away um an
understanding that why is so important
in data science. Um absolutely. Kind of
in the same vein, what do you think uh
what do you look for in the behavioral
interview uh
portion? So in a behavioral interview uh
what
um I like to learn from the candidate
is if he or she has the I think the
positive
um thinking or the the the positive
attitude
uh that he or she is really interested
to work with us not just some other
personal reasons or not uh things that
are not relevant to to our business. I
had a bad example from someone who was
doing an interview and I asked him uh
why our team why our company and he said
because I heard you guys have unlimited
vacations. So that's not a good answer.
Yes, I agree. Well, you know, I think
that that's um you know that's very
important. That's something that that
I'd like to reinforce is that when
you're applying for a company, people
can tell if you're passionate about the
experience. And I'm not going to tell
you you should only apply to companies
that you're passionate about because,
you know, sometimes you have to like
play the numbers and really like if
you're really trying to do a job, you
should apply to a lot. But you can
rationalize, you can think of reasons
outside of like the the holiday policy.
Um, why you'd want to work at a company,
you know, uh, let's say that, you know,
a company's in agriculture. I personally
don't have too much interest in that,
but there are elements of agriculture
that I really like. You know, I love
fresh fruit. I love these things. And
understanding how those all um, grow and
how they they they prosper is a very
fascinating thing. And like that's a way
that I could say, "Hey, this is a cool
experience because I'd learn uh about
that process and that would absolutely
come through in an interview." Um, you
know, I have two more questions about
the interviewing process and then you
know the first is how much programming
do you think is necessary to become a
data scientist?
So if this question is specific for data
scientist, I think uh the requirement is
going to be a little bit uh higher or
um obviously higher than someone who
will be working as a data analyst.
Uh and for my a point
uh the candidate should be able
to do the programming or do the coding
to build a model from scratch. For
example, a linear regression that's very
basic but also some other models that
are very popular like
um uh random forest or even deep
learning. I know uh there are there are
tools
nowadays that have enabled so-called uh
automated machine learning that has
become I think uh uh a great
productivity
uh tool set for those who do not know a
lot of coding to be able to also play
with the flow and to build some model
and test things out. But I think as a
data scientist um it has to be more than
that. And in terms of programming, it's
important for the data scientist himself
or herself be able to actually know uh
just the name behind those algorithms
and know the like the package know uh
those parameters when it comes to build
a model and actually code out the model
itself rather than just the um drag and
drop. Okay, very cool. And so just to
clarify that's you're talking at the
level of understanding like okay I know
to use scikitlearn uh linear regression
I know how to tune the parameters I know
how to use the same linear regression
model to make it like expens an
exponential regression rather than like
doing the vector math in the linear
regression and learning how gradient
descent works or going into that depth
of being able to like code it using like
numpy arrays rather than a pre-existing
package. Exactly.
Okay. Cool. Um the last thing um because
I know you you you are always looking to
improve your your business acumen. How
important is business knowledge and
business understanding for a data
scientist?
I like to answer this
uh relating to the previous answer that
I give on on different questions like as
an international student
um and also uh how to get the interview,
how many companies that I had interview.
I think the business knowledge is
something that is critical for a data
scientist and that comes to if that
company or that industry is something
that as a data scientist
uh he or she is willing to spend time to
learn the domain knowledge. It does not
have to be uh your
favorite company or let's not say
favorite. It does not have to be the
number one company that you you would
like to work with. Many times that did
not work. Uh people will will will apply
for different companies and from time to
time that may not be the top one on your
list. But uh as a data scientist working
with any company or any industry any
business I think it's important uh to uh
keep learning the business knowledge
within uh the company and from people
who you work with and also people uh who
you may work in the future. I think uh
um do not uh stop there just thinking
okay uh I'm I'm working with this team
so it's good for me to know what they
do. That's not enough. I think it's also
important to know the big picture of the
company and uh to understand u how the
company make money, what's the product,
what's the uh competitor
um and how those things are related to
your daily job as a data scientist.
Awesome. Well, I you know, I think if
you follow that philosophy as well, your
actual job satisfaction will be a lot
higher because you can see how your work
impacts the bigger picture, maybe even
the the profit of the company. And for
me, in my work, that's always been uh
the biggest motivating factor for me,
you know, wanting to get up to work and
and uh and putting hours at at my job
every day. So, you know, those are all
the questions I have. Do you have any
anything additional you'd like to add or
um or should we call it?
I think those are questions that covered
most of the aspects I would like to
share and again uh thank you for having
me. This is a great conversation. I hope
this is helpful.
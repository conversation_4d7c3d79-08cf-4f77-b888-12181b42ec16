Video Title: 2_6_Where_to_Showcase_Your_Projects
Video ID: O4wUH0qIpNM
Language: English (en)
--------------------------------------------------

Having a strong GitHub profile can do
wonders for your job opportunities. It's
just one of the many ways that your
online presence can also act as your
resume. In this video, I'm going to walk
you through some of the important
components of a strong GitHub profile
and hopefully this will help you
understand how to set up your GitHub so
that you're most appealing to potential
recruiters and employers. So, this is my
GitHub for my YouTube channel. At first,
you know, the first thing you notice is
I have a nice high quality photo. That's
something I recommend making sure that
it looks professional. You can also have
a little bit about yourself here. This
is again for my YouTube channel, but if
you want, you can talk about some of
your interest and the types of projects
that you can expect to see here. One
thing that's also relevant is that
GitHub or Git from the beginning is
actually a versioning system. It lets
you keep track of your code, keep track
of changes, and it should be used at s
as such. So, if you go in and you just
upload a a couple of workbooks every now
and then after you've completed them,
that doesn't necessarily show that
you've actually used GitHub or Git to
its fullest potential. So, if we scroll
down, we can see actually when someone
has contributed to their repos. And what
I think is relevant is if you see a
reasonable amount of green here, that's
a pretty good sign. That means that
they're fairly active and they're using
GitHub how you're it's supposed to be
used. So now let's go into an individual
project. So I have quite a few projects.
And if you have a ton of projects, one
thing that you might want to do on your
resume is specifically link to
individual projects. So a recruiter
isn't necessarily just overwhelmed when
they come to your your homepage. So the
first thing to look at here in the
project is actually
uh is the readme. So let's just go to
the readme document here.
And the readme is a highle overview of
your project, the structure as well as
the requirements. So the first thing I
always want to make sure I start with is
the actual overview, the goals of the
projects and the outcomes. In data
science, as I stress a lot in the
course, you really want to make sure
that you're communicating the value that
you're creating, not just what tools
you're using. So as you can see in this
first line, I talk about the actual
value that that this project creates. In
this case, it helps data scientists who
are looking for new jobs predict what
salary they can expect from a given
position based on the features that are
there. So here I also include the
requirements.ext file that allows
someone else who has not used your code,
who doesn't necessarily have all the
relevant packages on their computer to
quickly get all of the packages up to
date for what you're doing. I also
include all of the other resources that
I've cited to build this project. It's
really important to site your sources.
It's really important to make sure that
if you know if not all of it is your
work that you site where this other work
is coming from. This is relevant to me.
I mean I I've gone through and and done
this video on YouTube. So if you want to
understand an end to end data science
project, you're welcome to click through
this playlist. I believe there's seven
or eight videos where I walk through the
whole data science life cycle and show
you how I approach each step. That's
additional content. You definitely don't
have to have a YouTube video of every
one of your projects on there. The next
thing I talk about is the web scraping.
I look at all of the features that are
included. In this case, all of these are
relatively self-explanatory, but if some
of the features were confusing or might
not be intuitive, you'd want to go in
depth into what each of them mean. The
next thing I talk about is the data
cleaning, what I did in this specific
step. I also include the exploratory
data analysis and I think it's really
cool to actually include some pictures
of your findings and what they look like
there. The next thing I go into is the
model building and I talk about each of
the different models that I approached
this problem with. Finally, we have the
model performance and productionization.
So we can see that in this case the
random forest the mean absolute error
was the lowest and I thought that that
was probably the best model for this use
case. Additionally we're looking at the
productionization and I made a little
API endpoint so that we could you know
an outside party could connect to this
fairly easily if they wanted to actually
use it for for something. So, you know,
a couple people have actually gone on
and and and forked this and and built
some
additional projects or frontends related
to this, which I think is really cool.
So, the way that you actually edit the
readme is done in markdown, and there's
plenty of markdown guides that are
online for free. It's pretty easy to
use. Uh, so like a a single pound sign
is the is a header. These the asterisks
make bullets and so on. It's fairly
intuitive and that's something that I
can include a little guide on or I can
also link to if that would be helpful.
You can add pictures by doing some stuff
like this. But the main thing that you
really want to focus on here is
actually, you know, building out a
reasonable readme that's easy to digest.
When recruiters are coming on to your
page, they want to be able to understand
what your project is about relatively
quickly. And this overview up top is
really important for that. Something
that I could have done better in this
project is when I'm actually looking at
all of the the files and the folder
structure. I could have simplified this
a bit more and included one for perhaps
uh data, you know, a folder for data
cleaning for EDA for each of these
different steps. I can also go in and I
would actually recommend linking to each
of the actual GitHub um repos or the
workbooks that you're using for each of
each of the components. So I'd have a
link to the web scraping code from web
scraping. I'd have a link to the data
cleaning from this data cleaning in the
readme. So overall I think that those
are the main important components of
building a relevant and useful GitHub
profile. I again recommend checking out
this page. I have all of my resources in
GitHub at playing numbers. So you should
definitely check that out if you want
more information. On my YouTube channel,
I also do quite a few GitHub portfolio
reviews and resume reviews. So, that
could be an additional resource that
might help you understand this these
concepts through examples. So, in the
next video, I'll walk you through how to
build a Kaggle profile and the relevant
things there.
Video Title: 1_2_The_Data_Science_Knowledge_You_Need
Video ID: X75U_LduKmo
Language: English (en)
--------------------------------------------------

Before we dive into getting a data
science job, let's first define what a
data scientist is and the type of skills
that they need. A data scientist is
someone who uses a combination of
computer science, math, and business
logic to solve problems. Usually, data
scientists are dealing with very large
data sets. However, data science can
also be practical on very small samples
as well. Data collection, data cleaning,
data exploration, model building, model
explanation, and deploying models into
production are all things that data
scientists can expect to do on the job
as well. These phases are more broadly
described as the data science life
cycle. To be able to work through the
data science life cycle, you'll need
some technical and also some
non-technical skills. It helps to have a
good understanding of at least one
programming language and a query
language like SQL. These will help you
to manipulate data, implement relevant
algorithms, and visualize outcomes. I
personally have an affinity towards
Python, but R is also frequently used in
the field. To learn these, I recommend
looking at some of the other data
science courses offered by 365 data
science. You also need to understand
quite a bit of math. You should have a
foundational understanding of
statistics, linear algebra, calculus,
and also discrete math. Finally, to be
successful as a data scientist, you need
to be able to communicate your findings.
Tools like Tableau, Microsoft PowerBI,
Micro Strategy, Rsh <PERSON>y, and <PERSON>lotley
can be useful for telling stories
through the power of visualization. I
recommend learning the programming and
the math skills in parallel with this
course if you're not familiar with them
already. You should try to get them to
an intermediate level before applying
for jobs. How will I know when I've
learned enough? That's a question that I
get all the time. You'll be ready when
you can implement the projects that I
recommend in section two.
Now that you have a broader
understanding of data science, in the
next video I'll introduce you to the
types of data science opportunities
available on the job pocket.
Video Title: 6_3_Coding_Quizzes
Video ID: CcXg4i5gMq4
Language: English (en)
--------------------------------------------------

Some data science jobs want you to
perform a coding or SQL assessment
before they bring you into the office.
In these cases, both you and an
interview will go online and they'll
watch you work through a problem. These
are most common for machine learning
engineer positions, but I've seen them
given to general data scientists. For
data scientists, the coding portions are
usually on the easier side, much less
complex than what you might give to a
software engineer. They want you to have
a basic understanding of general coding
concepts like loops and variables. For
SQL, these questions can get a bit more
advanced. I recommend being familiar
with how the different types of joins
work, subqueries, self joins, and the
having clause. I've included a SQL cheat
sheet as well as some sample questions
in the bonus features. To put your best
foot forward, you should prepare
properly. I recommend going on glass
door to see what types of questions you
could be asked. I also recommend
practicing as much as you feel
necessary. And the reason I say that is
because you'll almost never feel
completely ready, but if you feel like
you're almost there, you've probably
studied enough. You can have a friend or
a study partner work with you over a
video chat to simulate the real
experience. When it comes to the actual
interview, I recommend asking if you're
allowed to use the internet if they
hadn't said anything previously. It's
also important to be able to talk
through your thought process as you
code. This skill is something that you
can cultivate in your practice by
working with a friend and talking your
code out loud. Even if you get the
question wrong, if they understand your
logic and it's reasonably solid, they
might help you out or let you through.
Now that you have the gist of the
take-home coding assessment, let's move
on to the final type of evaluation, the
written
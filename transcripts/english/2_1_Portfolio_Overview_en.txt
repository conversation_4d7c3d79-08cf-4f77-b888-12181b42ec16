Video Title: 2_1_Portfolio_Overview
Video ID: 5qVarvWDB1I
Language: English (en)
--------------------------------------------------

Hello and welcome to part two of the
course. In this section, I'll teach you
about data science projects and the data
science project portfolio. I'm a firm
believer that experience is the single
largest factor in landing a data science
job. It might seem surprising, but you
don't even need a job in data science to
gain experience. An internship or
previous data science related position
is great, but don't worry if you haven't
had an opportunity like that. The way to
get experience without having had a
previous job is through projects. And
projects can actually show something
beyond what jobs do. They show that
you're a self-starter and that you can
handle autonomy well. If you're willing
to spend your free time doing data
science, employers will also infer that
you really love and are passionate about
the field. I also believe that doing
projects is the single best way to
actually learn data science. Working on
a specific project also helps you to
focus your learning. It can be extremely
intimidating trying to learn the whole
field of data science. But when you do a
project, you only need to know enough to
get the specific outcome that you
desire. When you get your hands dirty,
you quickly realize your shortcomings,
and you can focus on specific areas to
improve. In this section, I'll help you
understand what makes a good data
science project, what types of data
science projects you should do, and how
to organize your projects into portfolio
on Kaggle or GitHub to make them
appealing to employers. In the next
video, I'll help you understand the
exact makeup of a data science project.
How are these things strung together?
Video Title: 2_2_What_Is_a_Data_Science_Project
Video ID: hzxihEIQgX0
Language: English (en)
--------------------------------------------------

Before we go out and build a portfolio,
it's important to understand the data
science project life cycle. These are
the phases that most data science
projects go through. Usually, we start
our projects with a planning phase. You
need to find a problem we want to solve
or data that we want to
explore. Next comes the data collection
phase. We need to go out and get our
data. I generally recommend collecting
your own data via a web scraper, but
there's also great resources like
kaggle.com and Google data sets where
you can find interesting data with
relative ease. The third phase is data
cleaning. This is where you take the
data you've collected and make it usable
for models. Now, it's on to the fun
part. After cleaning your data, you get
to perform exploratory data analysis,
otherwise known as EDA. In EDA, you
usually find interesting trends and want
to highlight them with eyepopping
visuals. After EDA, it's time to build
our model. We usually try different
models to see which one works best for
our specific use case. We should take
time to tune our models and make sure
that they generalize well to real data.
The penultimate step is model
productionization or model deployment.
In my opinion, this can be optional for
your GitHub portfolio.
Sometimes you want to make your model
useful to someone else as an API
endpoint and this is where you would
build that functionality out. Finally,
it's important to do a retrospective to
evaluate how you could have done better
on this project. In the real world, most
data science projects are never
finished. They're useful, but they're
iteratively improved over time. They're
just never done. Projects vary, and it's
okay if yours doesn't cover all of the
life cycle phases. As I mentioned,
sometimes the data that you collect is
very straightforward and is already
cleaned. Sometimes you're also only
focused on the exploration of the data
and might not be interested in building
any models with it. If you want a
complete project example, I have quite a
few on my YouTube channel. I even have a
project from scratch series where I go
through each of these phases in a single
video and really flesh them out. Still,
if you want to make a truly robust
project, you should try your best to
include all of these phases. Now that
you have a good understanding of the
data science project life cycle, in the
next video I'll talk about the types of
projects that you should do to stand out
to employers.
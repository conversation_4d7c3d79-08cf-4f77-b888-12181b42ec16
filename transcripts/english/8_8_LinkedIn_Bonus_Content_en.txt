Video Title: 8_8_LinkedIn_Bonus_Content
Video ID: JYSJeqYZDKk
Language: English (en)
--------------------------------------------------

Hello everyone, Ken here back with some
additional bonus content for the how to
start a career in data science course.
Today we're going to be talking about
your LinkedIn profile. I'm going to go
through mine and talk about a few of the
best practices there, some things that
will help stick out to potential
employers and recruiters. So, let's
start up top. I highly recommend having
a nice cover profile photo. This is
really important. People want to see
what you look like. People want to
understand that you know how to present
yourself professionally on a
professional networking website. Another
thing which I would also recommend
including is a very clean cover photo.
Mine is clearly some code, but you can
showcase a little bit of your interests.
You can put some branding up here if
you'd like. This is a great way to show
additional information and to
differentiate yourself from other
potential candidates. You also have the
option to include a description of
yourself. If you have a current role, if
you're an intern, something along those
lines, I recommend putting that here. If
you're just a student, I would also
recommend putting that in this section
here. I don't generally recommend saying
that you're a data science aspirant or
anything along those lines. I think that
if you're a student, if you're working a
role, that's generally what is going to
provide the most
information. Having that you're aspiring
to something is nice, but it doesn't
necessarily mean that recruiters are
going to be excited about hiring you or
or promoting you. They still think that
you're trying to be a data scientist,
not that you already are a data
scientist. So, let's go down to the
about section. So, this is something
that's fundamentally different than the
resume. Here you can talk about some of
your background, some of your interests
and really tell a bit more of a story
about how you got interested in data
science, some of the interesting aspects
of your learning. And also, you can
perhaps share what your projects are
focused on or some of your areas of
interest on LinkedIn. This is going to
get read a lot more than perhaps it
would on a resume. That's why I
recommend including it here. You also
have plenty of space here to include
relevant things. So if I am interviewing
someone, if I have someone coming in,
this is something I read on their
resume. Next, you can include any
relevant links. So your YouTube, your
Medium, you can also put your GitHub or
your Kaggle profile here. Those are
things that I really like to see. I
wouldn't say that that is completely
required, but it makes it all in one
place that you can share this
information. Uh that's one of the huge
benefits of LinkedIn. You can also see
your activities. So, I really like to
see when people are interacting and
engaging with the data science community
on LinkedIn. It can be as simple as just
liking a post from a data science
influencer. It can be commenting on
those posts or it can be producing your
own original
content. LinkedIn is absolutely a great
place to share some of your work. If you
have a project, you can put it on
LinkedIn. I think that's a perfectly
acceptable place. Usually when I see
people in my network that share their
project, share their own work, I try and
interact with it because that's
something I really encourage in general.
You're welcome to follow me on LinkedIn
as well. I'm always happy to engage with
people that are interested in learning
data science in general. Next, as you
can see, there's an experience section
that can be a lot longer than just your
resume. So here I go all the way back to
even some of the internships that I did.
Again, on your resume, if you have as
many different positions as I had, you
don't want to include them all. You just
want to include the recent and relevant
ones. But on LinkedIn, you can go into
longer form about this. I don't mind
seeing bullets about what you did in
each of your past work experiences here,
but they don't necessarily have to be as
in-depth as your resume was. For me who
is not looking for a new position, I
don't really have too much about the
work that I did at any of these roles.
But if I was going to be applying, I
would perhaps have a bullet or two with
some of the highlights of the work that
I did there. Going forward, you also
have your education. I take a lot of
pride in my education and I think you
should as well. I think it's really
important to include a lot of the
activities, societies, you know,
scholarships, any engagement with the
community that you had. These are things
that look really positively during the
interview process or for the recognition
of the recruiters. Next, you have
licenses and certifications. You can
absolutely put any of the certificate
programs that you did, any of the
courses that you've taken related to
data science. I love that you can share
all of this in longer form than you
could do very easily on a
resume. The next thing to look at is the
skills and endorsements. I highly
recommend, you know, giving endorsements
to other people. If you've worked with
someone else, it's really a positive
thing to share that that you believe
they're qualified in certain areas. This
is also a form of social proof. So, it's
a good way to actually demonstrate that
you have some of these skills that are
required. Going forward, I actually put
programming languages in under the
languages. I think that's the easiest
way on LinkedIn to show where where you
have these aptitudes. I don't think you
have to go into too much depth here. I
also like putting some of the awards
that I got when I was a student. These
are very dated now, but I still think
because it's at the bottom of the
profile, it's okay to include them.
Next, it's also very cool to show your
interests. I follow quite a few people
on LinkedIn and it's a great way to
showcase some of the things that you're
interested in if it's continuous,
especially with what you've described in
your about me statement. So, I think
LinkedIn is a great platform. It should
mirror your resume to a certain extent,
but there are a couple areas where you
can really expand on what you've said on
your resume and show a little bit more
information to anyone who's looking at
it. So, thank you so much for watching
and until next time.
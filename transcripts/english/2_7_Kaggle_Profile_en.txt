Video Title: 2_7_Kaggle_Profile
Video ID: HFAJFoX1YvE
Language: English (en)
--------------------------------------------------

Hello everyone, Ken here back with the
next section of the how to start a
career in data science course. In this
video, we're going to be focusing on
everything Kaggle. So, how to set up a
profile, how to go through a workbook,
and what all of this really means. So, I
think that you should have either a
GitHub or a Kaggle or both, but you
don't really necessarily need to have
both of these platforms. You just need
at least one extra place to showcase
your work. So, I recently created this
Kaggle profile for this use case. I'm
going to actually be continuing to add
to this. So, you can you can see what
some real data science projects look
like on here. You can go ahead and
follow me. It's just my name, <PERSON><PERSON>. So,
without further ado, let's jump into
what this profile looks like. So, <PERSON><PERSON><PERSON>'s
broken into four different types of
things. So you have competitions, data
sets, notebooks, and discussions. So the
discussions is a great place to ask
questions. And you can see how active
someone is by their ranking here. So if
someone upvotes any of your discussions,
you get a bronze medal. I think if it's
upvoted five times, you get a silver and
10 times, you get a gold. So an employer
can come in and see how active you are
in a community. I think that that's a
really good thing. A very similar thing
goes for your notebook contributions. So
if you find a data set in the data tab
here, let's open that in a new window.
There's hundreds, you know, more than
thousands of different data sets out
here. You can go in and you can do an
analysis on this data and if other
people upvote it, then you can get some
of these uh trophies here and that shows
that other people are finding value in
your work. I think that having notebooks
and having a discussions where you know
people are clearly engaging with your
work is a great thing in the eyes of
employers. The next thing is data sets.
So if you have you've scraped or you've
collected some very interesting data,
you're welcome to upload it here and
people can vote on if they like the data
set or not. The last thing that's
interesting is the actual competitions.
So there's plenty of Kaggle competitions
that are out where you can actually earn
real money if you place highly. this is
a a really cool badge of honor and
there's other additional benefits if you
do win like you know ai financial
financial gain. So I think that if you
were to win or place in the money or
even place in the top 10% of some of
these competitions that's a really
strong thing for your resume because you
clearly are understanding business value
actual financial value you're getting
paid for your data science work. that to
me is maybe not quite as good as an
internship or something like that, but
it is it is very competitive and can
absolutely help you in your career. So,
if you place in the top 10% of one of
the bigger competitions, you get a
bronze. Uh if you're in the top, you
know, uh 1% or top 10, this is a little
different based on the sizes of the
competitions. So, let's actually open up
what that looks like here. So, this is
the breakdown of the competition medals.
So, you know, if you're in the top 40%
in small teams and and and going
forward, this is what it looks like. So,
I highly recommend reading a bit more
about this and also just thinking about
how you want to engage with this
platform. I right now I'm mostly doing
discussions and notebook contributions.
I will probably experiment with some
more competitions in the future. So, you
know, this is something that I really
recommend doing. I haven't been looking
for a job and I'm not planning to for a
while, but I really should practice what
I preach and to start building out this
platform a bit more. So, feel free to
check back in with me. Hold me
accountable there uh as well. So, a
couple things. You want to have just a
nice bio. You want to say if you're a
student, what you're studying up here.
And definitely link to your other
resources. I think that that's a very
important thing to pay attention to. I'm
always talking about how to, you know,
share your your work, share your
resources, and definitely put all of
your links up here. Going down, you
should have just a nice professional
bio. Talk about some of the different
projects you like to work on, some of
these other things. And, you know, if
you do produce good work, other people
might go about following you. The last
thing down here is the activity. This is
very similar to on GitHub. If you're
very active in in a period of time, that
is a really good thing. People employers
like to see someone that's constantly
engaging with a community. So, let's go
in and and just look at a a sample
notebook that I've done. I've tried to
make this as clear to employers or a
prospective recruiter about what's going
on as possible. And I think that there's
a couple best practices. This is very
similar to how a readme would work on
GitHub.
So I I you know I personally don't
always use Jupyter notebooks but when I
am using one it's usually to explain
something and to be sharable and that's
something that you should also keep in
mind. So at the top I'm using markdown
and I just talk a little bit about what
the project is and I also talk about my
results that performed the best. I think
it's really important to provide an
overview of your work. If this wasn't
just a vanilla competition, I'd talk a
lot about what the data meant to me and
why I found it
interesting. Next, I clearly outline the
different steps of my project, all of
the things I'm planning to do. So, I do
some, you know, an overview of the the
trends in the data. I'm going to go
clean it. I'm going to explore a little
bit more. I'm going to build some
features and then I'm going to build a
model, uh, tune it, and then try a
couple different things and then
actually submit it at the end. So I
think the the code is not necessarily
relevant here but what is relevant is
actually going through and commenting it
out saying what each thing means.
Documentation is really important in
data science and you want to make sure
that you're using comments and some of
the markdown effectively so that people
understand your thought process. Seeing
the thought process of an interviewee is
is really valuable for potential
employers. They get to see, you know,
where some of your pitfalls might be,
but they also, if you do get something
wrong, can see that maybe you had the
right ideas, you just, you know, messed
up a line of code here or there or
something. So, I, you know, talk about
any of the decisions that I make. So,
if, you know, let's, for, for example,
I'm going to go down here and I'll
actually choose to imputee some of the
data and choose to remove some of the
data instead. I talk about why I
actually would make those decisions.
um I I don't just go ahead and do it. So
people want to understand okay why would
I imputee with mean or median whatever
that might be. Why would I remove rows
or or actually you know manipulate the
data in a different way. I also talk
about some of the feature engineering
tasks that I that I'd like to do and I
show how that impacts our dependent
variable which is survived. Again, any
decision that you make, you really want
to make sure that you clearly articulate
why you were doing it in either the code
comments or in some of the plain text.
Now, I talk about, you know,
pre-processing pre-processing the data.
Um, and I eventually start model
building here. One thing I always
recommend doing is having a table or
having a summary of all the results of
your different models. So, this is
untuned models. I just threw the data
into the model with base parameters to
see what we would get. And then going
forward after I actually do all of the
model tuning. I do the same thing again,
but I show the differences here. So I
think this is a really good practice.
People want to show they want to see how
your models performed and they want to
see how they eventually validate. I also
did uh some stuff with ensembles and and
I went through a couple different
processes and I explain hopefully very
clearly about the decisions that I made
there. So, the real takeaways with any
type of analysis that you do is you want
to be clear about all of the decisions
that you make. You want to be very clear
about your results and the value that
you create. And when in doubt, you
definitely want to just go into a little
more depth in the the markdown or in the
comments than um you'd error on the side
of going into depth rather than
vagueness. So I if if you want this
definitely go check this out on my uh
KGO profile. I will also be making a
YouTube video where I go through this
whole analysis. So I think that this
will hopefully be helpful to you in
getting started on Kaggle. It's a great
platform and I can't recommend it
enough. So in the next section I'll show
you how to write about projects like
these on your resume.
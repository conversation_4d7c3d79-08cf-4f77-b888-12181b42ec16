Video Title: 6_1_The_Types_of_Take_Home_Tests
Video ID: i82iv1F1UnU
Language: English (en)
--------------------------------------------------

If you make it through the phone screen,
you can expect to be given a take-home
assessment. These tests help to evaluate
your technical skills and your ability
to clearly communicate data science
concepts. There are generally three
different types of assessments that you
can be given. The first type is a
take-home data set. In this type of
assessment, you'll be given data that
they want you to analyze. Often, they'll
give you a target that you should build
a model around. For example, they may
want you to create a model with the
highest accuracy based on a dependent
variable of their choosing. It's less
common, but they might also just give
you a data dump and expect you to come
to your own conclusions. The second type
of test is a SQL or coding assessment.
For these, it's common for companies to
do them live. You log into a platform
with an interviewer and they watch your
screen as you code through a problem.
If you don't have one of these before
the interview, they might do one during
the in-person step that we'll talk about
in the next section. The last type of
assessment that you can expect to see is
a written test. These are far less
common than other types. Basically,
you're given 3 to 10 questions about
programming, data science, and
statistics. For all of these
assessments, they'll tell you the
timeline that they're expecting for you
to get your results in. These timelines
can be anywhere from 2 to 3 days or 2 to
3 weeks. In the following sections, I'll
give you some tips on how to prepare for
these different types of tests and how
to impress with your answers. In the
next video, we'll start with the
take-home data set problems.
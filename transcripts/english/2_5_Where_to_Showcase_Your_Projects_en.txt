Video Title: 2_5_Where_to_Showcase_Your_Projects
Video ID: HFk1A0GrUpQ
Language: English (en)
--------------------------------------------------

After you've done your projects, there
are a few different places that you
should showcase them. You should look to
put your projects in a Git repo on
GitHub, Bitbucket, etc. on Kaggle or on
your personal website. In the bonus
features, I have a quick tutorial on how
to set up a GitHub hosted website for
free. You can also use other services to
create a website if you like. You want
to make sure that your code is clean and
that you clearly explain the project. In
the next two videos, I'll give you some
examples of how to properly format
projects on GitHub and kaggle.com.
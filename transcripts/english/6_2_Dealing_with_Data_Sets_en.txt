Video Title: 6_2_Dealing_with_Data_Sets
Video ID: 4Y-zCI7QbyM
Language: English (en)
--------------------------------------------------

It's becoming common practice for
companies to send candidates a data set
and expect them to analyze it. This can
come in one of two different varieties.
The first type, the data can be
open-ended and you'll be expected to do
an exploratory analysis. In the second
type, they want you to build a specific
type of model to solve a problem. For
both of these project types, employers
will usually recommend that you spend
about 4 hours on them. I think you
should spend as much time as you need to
get results that you're comfortable
with. Also, don't stress out too much
over these. They should be evaluating
you based on what a normal data
scientist can do in that short period of
time. When dealing with either of these,
make sure that you watch out for the
following things. First, you want to
look for missing data and null values.
Make sure that you know how to deal with
these cases and you can explain why you
decided to either remove or imputee the
values in a certain way. The second
thing you should look out for is sparse
data. Know what models work well with
sparsity. The third thing is data with
different types and distributions.
Understand how models handle non-normal
distributions in data. The last thing
you should pay attention to is
understanding data of different types.
For the exploratory analysis, they want
to test two main things. First is your
understanding of the tools used and the
second is your business logic. These can
be intimidating because there's
generally very little direction. They
might ask a broad question like explain
why profit went down this year. These
are my specific pieces of advice for the
exploratory analysis. First, evaluate
the data. Look at the shape of the data
frame, the data types, and the
distributions of the continuous
variables. Box plots and histograms can
be very useful here. I recommend using
visuals as much as you can. Also, take
note of any unusual cases that you see.
The second thing you should do is choose
a few features that you want to
evaluate. These should be based on what
can provide the greatest hypothetical
business value based on the data set. I
recommend using a correlation plot to
see how these variables are related to
your other features. You can also use
group buys or pivot tables to understand
how values in these variables differ
across different groups. With this, you
should be able to understand at least
one highle trend or insight. Next, you
should build a model to predict or
understand these variables better. Be
clear to explain the business value for
this method. For the take-home tests
that are more focused on model building,
this is a different story. First, you
have to determine if the model that
you're building is for understanding the
data or maximizing predictive power. In
general, you should build both types of
models and talk about the benefits and
drawbacks of each. When working on these
types of projects, I recommend the
following process. Understand your data
and create features. One of the best
ways to improve a model is to give it
better data. If you can find data easily
or create more meaningful features from
what you have, this is a huge asset.
This is also called feature engineering.
The second thing you should do is to try
a few different models and explain why
you chose them. There are tons of models
out there that you could use. Some are
better for interpretability or certain
types of data. Make sure that you can
clearly explain the math behind the
models that you use. The third thing you
should do is use cross validation
properly.
Next, you should also tune your models
and explore ensemble
approaches. The fifth thing you should
look at is you should make your model
easy to productionize. For both of these
projects, make sure that your code is
organized and well commented. You'll
usually be working with other data
scientists, and this is something that
can make others want to work with you.
There's also a chance that they offer a
data analysis in the in-person interview
or they talk about the analysis that you
did in this take-home portion. The same
concepts still apply. However, you'll
want to likely streamline this process
if you're doing it in person. You also
want to be able to talk through the
steps of your analysis clearly. If you
have a good understanding of the data
science life cycle, you'll likely be
ahead of other candidates. Now that we
have a good understanding of the
take-home data assessment, in the next
video I'll show you how you can excel at
the coding assessments.
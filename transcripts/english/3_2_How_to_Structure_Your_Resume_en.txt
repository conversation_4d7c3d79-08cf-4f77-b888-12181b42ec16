Video Title: 3_2_How_to_Structure_Your_Resume
Video ID: yVMddoP0b2M
Language: English (en)
--------------------------------------------------

When building a resume, it's important
to optimize it for humans as well as
computers. Many companies use automated
systems that do an initial filtering of
résumés based on keywords. You want to
make sure that yours passes both the
human and the computer scan. For
building your resume, using Microsoft
Word is perfectly acceptable. I
personally use Adobe Illustrator, but
there's no reason to make that extra
expense. You should use clear, legible
fonts and go for a modern look. You can
add some personal touches as well, but I
wouldn't overdo it. Unless you've been a
PhD or been working in a relevant field
for a long time, we're talking 10 to 15
years, you want to keep your resume to a
single page. As a data scientist, it's
extremely important to convey
information in a clear and concise
manner. And by keeping your resume and
your work to a page, you can begin to
show that. At the top, you should
include your name, the state in which
you live, and a valid email address. I
would omit putting too much personal
information here. Generally, a phone
number is fine, but you might want to
include that in the cover letter
instead. You should also include any
relevant links, your GitHub, Kaggle,
personal website, YouTube channel,
Medium, LinkedIn, etc. Next on the page,
I recommend putting your technical
skills. Employers want to know if you're
qualified for the position that you're
applying for. And the first criteria for
this is if you have the same skills that
are mentioned in the job posting.
Putting this front and center gives them
one more reason to put you into the pile
designated for the next
round. I recommend only putting
technical skills like programming
languages, packages, and learned
concepts in this section. Examples of
these would be Python, R or Scala for
languages, pandas, numpy, scikitlearn
for packages, and clustering
classification and regression for
learned concepts. You can also include
things like scrum, five wise analysis,
or other project management related
concepts or
ideologies. You generally don't want to
include things like organized,
hardworking, problem solver, detail
oriented. These are extremely subjective
and these qualities should be shown
through your projects and work
experience rather than told to the
recruiter. Next, you want to include
your most recent experience. If you're a
student or recent graduate, I recommend
putting your education here. If you've
had previous work experience, then you
should put your work section next. For
students, you should include your major,
your relevant courses and activities
here. If you made the deans list, were
involved in any related clubs, or had
any unique experience, also include
them. Diversity of thought is extremely
important. So, if you've studied abroad,
started a club or group, definitely show
it here. Also, if you've won any awards
or scholarships, be sure to include
them.
You can include certificates, boot
camps, and online courses in this
education section as well. For those
coming with work experience, make sure
you highlight relevant work. Also, make
sure to talk about the projects and
experiences that show characteristics
that would make you a strong performer
on the new job. I've seen many résumés
where someone clearly had relevant
experience, but on their resume, they
only briefly mentioned it or hit it at
the bottom. You don't want to fall into
this category. You have a small amount
of time with a recruiter's eyes on your
resume and you want to lead with your
strongest and most relevant things.
After these sections, you want to
include your project work. I recommend
using the same formatting that you did
for your work experience section. You
want to name each project like it's a
job and talk about the value that you
created with it. You can talk about the
algorithms that you used as well, but
you want to be more focused on the
business value that you've driven.
especially in the early stages. This is
what recruiters will be looking for.
Finally, at the bottom, I think it's
nice to include something short about
yourself. Many people include a few of
their hobbies, favorite sports teams, or
places that they've traveled. For
example, on my resume, I include the
five books that I've read most recently.
These are all great starting points for
a conversation. Now that you have a
grasp of the résé layout, in the next
video we'll address how to write about
your work and your projects.
Video Title: 2_3_The_Projects_You_Should_Do
Video ID: 2cjyvTi92yM
Language: English (en)
--------------------------------------------------

Now that you understand the components
of a data science project, it's time to
start building them. At this point, I
always get many questions about the
types of projects that you should do.
First, the best part about a data
science project is that you get to
choose the topics that are interesting
to you. Projects that are unique to you
and your interests are very appealing to
employers. Additionally, if you're
interested in a specific industry, I
recommend doing a project related to
that field. For example, before getting
my job in sports analytics, I had
already done four to five projects with
data related to that specific domain. In
data science, there are three main types
of problems to solve. You have
regression, classification, and
clustering. In these projects, you
should be comparing the performance of
various different algorithms that fall
under that problem type. I recommend
doing at least one project for each one
of these three problem types. I also
recommend doing at least one project on
what I would consider to be an advanced
data science concept. I consider the use
of deep learning, image classification,
or natural language processing all
falling into this advanced concept
category. At the very least, you should
have four to five projects in your
GitHub or Kaggle portfolio. Better yet,
if you can place in a Kaggle
competition, that's an additional badge
of honor. If you can contribute to a
GitHub community, that also looks
really, really strong. Now that you have
a good idea about the types of projects
you should work on, in the next video
I'll talk about some of the ways to
differentiate your projects so you stand
out to employers.
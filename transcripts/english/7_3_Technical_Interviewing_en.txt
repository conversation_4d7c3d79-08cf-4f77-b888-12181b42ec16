Video Title: 7_3_Technical_Interviewing
Video ID: JhoXnBaGPgU
Language: English (en)
--------------------------------------------------

The technical data science interview can
come in multiple varieties. They'll
either ask you about coding and SQL or
data and math related problems. There's
a good chance that they'll have you take
an in-person technical assessment on a
computer as well. I won't talk much
about the computer assessment here
because the advice for excelling in that
is the same as the take-home section.
I'd like to focus on how to answer
technical coding questions and data
science specific math questions here at
a high level. Success in both of these
types of questions is all about practice
and pattern matching. If you've explored
enough of these questions online and in
the bonus resources of this course, you
should be adequately prepared to pass
this interview. Still, there are some
additional tips that can help you along
the way. For the technical coding
questions, you'll frequently be asked to
solve your problem on a whiteboard. I
personally think that this is a bit of
an antiquated process, but it can show
them a bit of how you think on your feet
and handle pressure. When you're going
along, make sure that you talk your way
through your code. Again, this is a
skill that you can practice. Even if you
don't get the question totally right, if
they agree with your logic, this can
score you points. If an interviewer
understands your logic, they might also
give you some slight corrections that
could help you out. Many people get
stuck during these questions and it's
perfectly normal. If you get stuck, keep
talking out loud. Say where you got
stuck and the potential directions that
you'd like to take the problem. Most
interviews will give you a little bit of
guidance to keep you on track. Again,
this practice is to understand how you
think. So, if you illustrate that by
talking through the problem, there's a
good chance that they won't give you any
negative marks. If you're still stuck,
it's okay to ask for help. It's
definitely better than standing there
awkwardly for too long. For technical
statistics and data science questions,
interviewers will often take you through
a case study problem. They'll ask you to
explain some of the main decisions that
you make at each step of the data
science life cycle. They may also ask
some basic math questions related to the
algorithms that you use. To prepare for
this, I would make sure that you
understand how randomness is used in
data science. I'd also recommend
understanding in what circumstances
you'd use one algorithm versus another.
In the bonus features, I have a list of
the common questions and answers that
you're likely to see. Again, it's okay
to get stuck here. Just say what you
think the answer is, and be clear that
you'll have to do more research on the
topic. Now that you have a framework to
succeed with the technical questions,
let's move on to the interview
follow-up.
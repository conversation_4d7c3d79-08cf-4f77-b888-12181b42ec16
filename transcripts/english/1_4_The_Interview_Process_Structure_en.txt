Video Title: 1_4_The_Interview_Process_Structure
Video ID: kYoKlNbWwCc
Language: English (en)
--------------------------------------------------

Like data science roles, interviews can
vary greatly by company, but for the
most part, there are still some
similarities that hold true across the
board. Most data science interviews
start with a phone screen. This is
usually a conversation with a technical
recruiter to assess your interest in the
role and ask about some of your past
experience. Sometimes you'll also be
asked some technical questions during
this phase of the interview as well.
Next, there's often a technical
assessment. For data science, you're
usually given a data set to analyze. I
personally think that this is the most
practical evaluation of relevant skill.
Some companies also ask you to complete
a shortwritten response to a few data
science related questions. You might
also be even asked to do a virtual
coding or SQL assessment. Again, for
most data science positions, these are
not too demanding compared to something
that a software engineer would have to
do. For example, if you pass the
assessments, you'll be asked to come
into the office for an in-person
interview. In most cases, you interview
with three to six people. Expect to meet
with the data scientists on the team,
the data science manager, and at least
one person from HR. You might even have
the opportunity to interview with the
CIO or the CEO of the company if it's
small enough. In these interviews,
you'll be asked behavioral questions and
also some technical ones. In this
course, I'll break down each of these
steps and show you how to leave your
best impression across the board. Now
that you're familiar with the interview
process structure, in the next section
I'll walk you through exactly what
companies are looking for in data
scientists.
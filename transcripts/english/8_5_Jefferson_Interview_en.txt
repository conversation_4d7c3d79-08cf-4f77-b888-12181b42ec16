Video Title: 8_5_<PERSON>_Interview
Video ID: Hpe_olsHp0I
Language: English (en)
--------------------------------------------------

All right. Hello everyone. We have a
special guest interviewer. His name is
<PERSON>. He is the head of data
science at a consulting firm in Chicago
where they again specialize in data
science. I think <PERSON>'s story is
that he's he's is really interesting
because he's been able to rise to this
position without having an advanced
degree in data science. So, you know,
his story can really be valuable to you
in terms of understanding the actual
data science interviewing process and
how we kind of went up the chain of
command, but also because he is the
director of data science, I think that
he can provide some really uh unique
perspectives on things that he looks for
in prospective candidates. So,
<PERSON>, I'd love for you to expand a
little bit more on your background uh
and maybe tell just a story about your
relationship with the field. Yeah. Uh
thanks, <PERSON>. Um, so as <PERSON> said, my name
is <PERSON>.
Um, I came from a background in physics
and
mathematics. Um, and I kind of got into
data science almost as kind out of
necessity and kind of chance. Um, my
original plan was to get an advanced
degree. I was going to do a PhD in
physics. um and kind of decided against
that for having to pay back student
loans. Um and uh and then I found myself
kind of with a skill set that was that
was kind of prime for data science.
Um and um and from there, yeah, things
have kind of snowballed and uh um had
quite an interesting journey. I mean
these last 10 years um in the fields of
computer science and machine learning
and you know data science have been
quite interesting. So um yeah there's
kind of kind of a lot going on there.
Absolutely. Well, I I know I know it
might be kind of turning back the clock,
but I'd love to get any insights from
when you were interviewing for your
first couple data science positions and
one, you know, if anything has changed
over that process. Uh but also two, you
know, what were your big takeaways? What
would you recommend that people uh
maximize on? Absolutely.
So, yeah. Um so, let me just start off
by um kind of reiterating. So I studied
physics and math in school and um I you
know the coursework was I would say
definitely helpful especially for
understanding a lot of the
um a lot of the mathematics and like
fundamentals of data science and machine
learning. Um but it was actually me
being involved in research in uh
computational cosmology that really
prepared me for data science. So it
built up a lot of my programming skills
and you know skills to model data and
collect data and write papers and and
and you know version control and like
all these different things. Um and that
kind of built up the skill set.
Um so going into you know the interview
process I mean initially it was ex
extremely difficult. I was um let's see,
I graduated in 2013. Um you know, the
economy still wasn't super great, but it
was doing all right. And
um I applied, cold called like I'm I'm
not kidding when I say this, probably a
hundred different positions. Um and
really got nowhere. Um it wasn't until I
started working with a recruiting
agency that I actually got somewhere
with a couple positions. Um, and that's
ultimately how I got my first job was
um, first being uh, being hired by a
recruiting agency and them placing me in
a role um, at GE. Um, so it was kind
of an interesting way to get into it,
but I honestly think that, um, if that
hadn't happened, I probably would have
just, I mean, I think I kind of would
have been stuck spinning my wheels for a
while, maybe until someone took a chance
on me. So, I don't think it's a bad way
to potentially get your first job. um
because there's a little bit more of
trust that companies place with a
consulting agency that they have
previously vetted a candidate um as
opposed to them sort of just taking a
chance on a random person that sends
them one sheet of paper. Do you have any
tips or recommendations for how to get
um in with one of these recruiting
companies? You know, one thing that I uh
that I that I definitely emphasize in
the course is the importance of
referrals or the importance of having
like a warm connection. Um the the
previous interview we had, you know, he
he did 200 interviews. He was an
international student and he the only
one that he got through with or the only
few that he got through with was because
of referrals or some sort of previous
connection. So, anything that you can,
you know, speak to about how to develop
those relationships with with
organizations is really valuable here.
Yeah. Um, I don't it I mean, so I don't
think I've actually ever gotten a job
that I just like sent my resume to. Um,
it's always been through a connection
whether that be from a recruiting agency
because or because I know someone or
someone submitted me or you know,
however it it happened, it was always
through like a referral or a
relationship. Um, so if it were me, that
would be my very, you know, if I'm
looking for a job, that would kind of be
like my first move is to try and reach
out to to contacts
um, and see who's there that can sort of
vouch for me. Um, and if you don't have
that, that's okay. You know, I didn't
have that when I started. That's why
when a recruiting
agency eventually like got back to me,
they were happy to work with me.
Um um I think mainly that process I
think mainly you know happened because I
was polite and hungry for work and um
and then you know they also had to vet
me internally before they would even you
know send my resume out to potential
clients and everything. Um but after
that they were quite helpful and you
know pretty good at being able to get me
interviews at places uh until I found a
job. Awesome. You know, I think that um
you know, while we're on kind of the
subject of like getting the even the
first interview, what do you think that
people can do and what skills do you
think are relevant to have to be able
to, you know, get through that pass
through that first phase to at least get
like a phone screen or to get a foot in
the door there?
So the very I
mean it's kind of there there's kind of
two things that I would say about this
one is
um the interview processes like you know
in general are kind of split up into two
prongs. It's like you know um technical
interviews and then sort of like um
personality screens and you know stuff
like that.
Um, getting your foot in the door can be
difficult.
Um, if you're not able
to h, how do I put this? I think what's
valuable
is being able to perform well in one of
those categories.
um or making a connection with whoever
you're speaking to cuz like ultimately
interview processes is like um trying to
forge a contra tr contract of trust
between two parties. Like that's really
all it is. Um so if the person
interviewing you can start to trust you,
then I think you have a much better shot
at anything that you're doing. And this
goes for technical screens as well as
like personality screens. So um I'll
give you a quick example. Like when I
was still h uh interviewing for that
position um to be represented by that
recruiting agency, they had me go and do
like a a Python screen or something. And
um they were kind of a little weary
before that and like yeah, okay, you
know, didn't really show much interest.
But then when I scored really well on
that, they were like, "Okay, let's do
this. Like, we got all these clients,
like we're going to send you out." And
it was at that point where they they
they like trusted me, you know, they
they knew that like I was capable of
doing what they what they needed or at
least had some of the skills to do it.
And then they were much more willing to
act on my behalf. Um, and so I think
establishing that kind of like trust
with someone in some way is is super
important. Um it doesn't have to come
from a technical screen, you know, it
can come from a personality thing as
well. You know, speaking with someone
and them understanding your capabilities
in that way can also help that.
You know, can I help? No, absolutely. I
think you hit the nail on the head. You
know, one way that I generally recommend
that you try and establish that trust is
through projects or through research
like you've done. um you know do you
have any specific insights on to you
know like when you're you know now
transitioning to um the actual
interviewing side where you're
interviewing people do you have any
insights on like what you look for in in
someone's projects or in their portfolio
or or or um that they can offer to you
that would kind of establish some of
this trust. Yeah.
Um past projects are good. I mean
there's again you know there's no quick
and I mean the good thing is or maybe
the bad thing is there's no quick and
easy way to answer any of these
questions but in my opinion there's kind
of two things that can happen here.
Um projects and past experience
um can be really good in establishing
that trust. um especially if the role in
which you're interviewing for
um
is is sort of like wellestablished or
the hiring manager or the organization
really knows what they're looking for.
Um if you match that, you know, in some
way or you can t or you think that you
do and you can tailor your resume or
your past projects or whatever you have
to bring to the table to sort of fit
that. Um it's a huge advantage.
Um past projects are important. Um past
work is important.
Um but ultimately for me like you know
when I'm looking for candidates
um again it's it's
me what I need to know is can this
person do the job? Um, and there have
been candidates that have had a ton of
past experience where I definitely did
not think they could do the job. Um, you
know, past experience in terms of jobs
and projects, but just they were just
not a good fit for the role. And that
could be because we knew that they were
going to be working with a really tough
client or someone that was that you know
the client they were going to be working
with
was not very strong technically like
they were accomplished business leader
let's say um and the person themselves
was an incredibly accomplished uh you
know technically but didn't really have
the best communication skills they just
weren't going to succeed in the role.
Um, so you know, I guess what I'm saying
is you can do everything right and you
can have amazing projects and an amazing
portfolio and amazing, you know,
personality and still not be a good fit
for the role. And that's okay. You know,
try not to take it personally. It gets
really hard being rejected hundreds of
times. It happened to me. Um, but I
would say what I look for is building
trust.
So anything you can do for that like I
said projects are important past work
experience is important but that's why
ultimately interviews I think the
interview process um is the most
important and you know even though we're
in a highly data driven field a majority
of the interview processes are not
really data driven they're very very
subjective um it's a lot of do I like
this guy's talk yeah yeah exactly or
girl sorry Yeah. Guy or gal. Yes. Um,
you know, with that in mind, what are
some things that candidates might do
that would be an immediate like turnoff?
You know, I a lot of the times someone
will come in and you're like, "Wow,
their resume looks great and then, you
know, something can can flip really well
that might make you think that they
wouldn't be a good fit for the role or
something like that." Yeah. I think um
if someone is making it apparent that
they are
um like sort
of either disinterested or like they are
don't have a a strong work ethic of like
don't care. Um it can it can be a big
turnoff
and I mean you'd be surprised. I know
some people out there would probably be
like well who would do that? I mean I
don't know. There are people that do it
though, you know. Um you see it. So
that's an immediate turnoff. like you
know I don't if someone doesn't want to
be there during the interview or just is
you know not really thrilled with things
like it's it's a it can be a turnoff. Um
um yeah so that's probably what I would
say is the biggest turnoff. um as well
as like I said
like maybe you know indications that
their work ethic is is not um is not
high enough I guess for lack of a better
word. I agree. You know I think in some
sense especially like in your realm or I
guess we're both in consulting
technically one thing that really
bothers me is talking bad about a past
employer. That's something that I see
quite frequently and um you know when
when you are a in consulting and you're
working with multiple clients that's
like something that you you definitely
can't have you know that's something um
that is really important to be to keep
that level of professionalism and most
people think of data science as a very
highly technical role but there is still
a large amount of that relationship
building that that communication and to
me that's just one like one other red
flag where it's like you know you can't
be doing that even if it's you know
you're talking to one of our
stakeholders like we have to kind of be
able to have that respect in their lines
that you that you really don't uh that
you really don't cross. Um, definitely,
you know, and to add to that, I would
say like there's a there's a there is a
fine line between like being honest
about a past work experience and and
like being, I don't know, uh, like
negligent about it, you know, like it's
totally fine if you had a bad
experience, you know, with work, a past
employer, and you're honest about it or
something. But um but yeah, I mean I
agree that
like having a level of professionalism
about not maybe like slandering them is,
you know. Yeah. So one thing that I get
asked a lot of questions about is you
know having advanced degrees, having
certificates, having uh doing going
through boot camps, you know from a
hiring perspective, how do you view all
those things? you know, you can either
look at them in isolation or you can
talk about them as a group. You know,
are they are they important to hiring?
Uh or are there things that you can do
to um make it so that you don't
necessarily need those things uh to be
able to get in the door?
My honest answer is that
um a
degree is basically worthless and I
don't care about it at all in so like in
so far as
[Music]
um the the value that I see coming from
any types of advanced degrees or degrees
in general. I mean there you can have no
degree at all is just the opportunity to
do work or be exposed to certain things.
So if people are um you know writing
papers or or publishing great you know
articles or whatever it may be even if
they don't have a
degree I think that's totally fair and
and shows great initiative
um just as much as maybe someone who has
a degree or an advanced degree and
hasn't done any of that. Um I don't
really weight
degrees at all.
Um, I I this gets into a bit of my
personal beliefs, but I think that um
it's kind of ridiculous that we make
people get a degree in order to get a
job. And it, as you can see, I think
some of the larger tech firms are
starting to move away from that. And,
you know, I would agree with that. I
think
it's of very little importance. Um, but
what I would say is
the potential exposure to the to
opportunities andor research andor
um um learning that can occur there is
what's important. So, as long as it's
demonstrated in a in a in another way,
there's really no difference to me
between candidates that have a degree or
an advanced degree and candidates that
don't. Awesome. I I agree 100%. I think
that degrees or like the need for
degrees in data science is a bit of a
well it is for the most part a relic of
the past right when data science was a
very new field and they're saying hey
how do we evaluate if this person would
be qualified for this role an advanced
degree was like the first lowhanging
fruit to be able to evaluate on now that
we've grown and there's so many
different ways to be able to show your
aptitudes you know whether it's a Kaggle
competition or whether it's some of
these other platforms the need for that
is is dramatically lower. I mean, I will
say, you know, I I do think a lot of
companies are still behind and they do
evaluate based on this and so it can be
beneficial to have some like broadly I
don't think by any means it should be
necessary to have one because there's so
many ways to supplement. Uh 100% agree
like philosophically and personally, you
know, I don't agree with like the sort
of the need to have a degree. Um, but
Ken, totally right. There still are
employers that do. But I just want to
make a quick note. I was actually um
looking at uh I don't know. I was
reading some news articles or something
and it was like I got some recommended
feed uh in my Google app and it was like
the top 10 PhD and data science
programs. Um not a single one of them
was below $50,000 a year. And PhDs are
supposed to be free. Come on guys. Yeah,
I Yeah, I know. That's ex and there's no
way no way that I'm ever going to put
myself in that much debt again to get a
slip of paper. I mean, I will second
that. It's just not worth it. It's just
not worth it. So, um that's yeah, that's
my two cents, but I I agree with you.
There are some employers that do place
that importance, you know, on those
things, but
you can maybe stay away from them and
then they'll be forced to change. Yeah,
it's possible. So I have kind of really
three more questions. The first two are
about a little bit more about the
interview process. You know, like are
there some specific things you look for
during the technical interviews? And
then I'll ask the same about behavioral
interviews. Yeah.
Um I would say it there's a couple
things. The very first thing that I'm
looking for is like aptitude and the
ability to self-e. Um I always hire uh
aptitude over outright skill. skills can
be learned. Um, aptitude and like the
the willingness to learn and to self
teach is a is a lot harder for an
individual to learn. So, um, I look for
that in in every part of the interview
interview and especially in the
technical screen. So, I'm looking for,
you know, if we're going through a
difficult
problem, I'm looking for them to be
asking me questions and I'm looking for
them to pull up a web browser and start
googling something. Like, a lot of
people think that's wrong. No,
absolutely not. I Google and look up
code all the time, constantly. Um, you
know, the the ability to like just go
get stuck in and figure something out, I
think, is the most important skill. um
maybe tied with that and or second with
that is communicative abilities. Um and
that's
because you know even if you're not
working directly with like a business
stakeholder, you're still going to be
collaborating with other data scientists
or data engineers or other people and
you know if you can't effectively
communicate what you're doing and what's
going on then it's going to be a mess.
So I mean at the very least you have to
be able to communicate with your boss,
right? Or else you're going to get
fired. Yeah, exactly. So, if you know if
they're not able to like effectively
communicate to me what they're doing,
then that's a you know, that can be a
red flag. Like there are times when, you
know, I've done an interview and
someone's approaching a problem that I'm
not quite following and I don't totally
understand. So, I'll ask them and, you
know, try and have them walk me through
what they're doing. And if they struggle
with that to even articulate what it is
that they are developing um that can be
a little bit of a red flag. Awesome. I
mean honestly you hit a couple of things
I talk about in the course like right on
the head. Yeah. So I mean the first
thing I I'm pretty much always say that
when you are doing one of these
technical things, one of the first
things you should ask is can you use a
web browser? Can you use the internet?
Right. I mean like I don't know any data
scientist that can just sit down and
like just pop out a single one. I
guarantee it without googlingite quite a
bit of stuff. Um, and you know, one of
the things that I do really recommend
practicing is talking through your code,
talking through your thought process,
because you know, correct me if I'm
wrong, the things that you're looking
for uh during the technical interview is
how someone thinks, not if they can
necessarily solve the problem at hand.
It's are they going about the correct
process to be able to solve other
problems that are more relevant to your
company. So, yeah, that's 100% right. I
mean like look there's never ever ever
going to in your career going to be a
problem that you solve within like a
real problem that you solve in 30
minutes or an hour. Never. I'm sorry but
like so the interview process is a
little bit flawed in that way as like
these
one-hour technical screens can are
trying to be used as like a proxy for
someone's you know ability to do a job.
That's why I think it's important to
focus on someone's ability to problem
solve because in the real world these
things are going to you know the
problems that you come across take a
long time. These projects are not just
super you know it's not a few hours a
weekend. A lot of these projects take
months. So you
know I'll add in there as well
perseverance. Someone has perseverance
they're going to do well. Do you think
so a lot of companies offer like a
take-home like data set a technical
screen in that sense is that something
you generally prefer to see? I mean I I
personally think that's a better
valuation. Um but from your perspective,
you know, do you guys do that? And if
not, is there a particular reason that
you guys don't? Um I I personally when
I'm the person interviewing I prefer
that um because I don't typically when
you know I don't typically do well in
high pressure environments with someone
looking over my shoulder and there's a
timer on my screen like I just don't
never happens in the real world. No. No.
So I, you know, when in the real world
when I'm working on a problem, like I'll
work out, you know, at work and then,
you know, I might even get up and just
pace around or walk around. I come home,
you know, then I'll forget about it.
I'll come home and maybe I'll have an
idea and I'll get out my laptop and work
on or something and then, you know,
maybe I'll have an idea, you know, a
couple hours later or something. So it's
not like that's not the way people work.
Um, so I'm in favor of take-home stuff.
The only um the only issue that I have
with it is that um they can take up a
fair amount of time and
um they can verge on like I would
say a asking like an unfair amount of
work from someone especially when they
already have a full-time job and they're
probably interviewing for more than just
that one position. Um, I remember there
was a just as a quick anecdote, there
was a point in my life a few years ago
when I was interviewing for like I think
I was I narrowed it down to like three
jobs and I was on like the final rounds
and all of them had like take-home
things and all of them wanted to do it
within like the next week and so I
literally had to spend an entire week
like basically two days for each job
doing these take-home exams and then
ended up getting I I think only one of
them ended up going further than that
And it was just like, you know, two of
them didn't even contact me after that.
And it's like, if someone if I'm going
to ask someone to do a take-home exam,
then I need to show them the respect of
giving them at least that much attention
in return and um you know, working with
them and being flexible on when they can
do it. Fair. Totally fair. I I think
that that's um I think that's a good
policy. And you know I think if
companies would give a little bit more
of a time horizon that's also something
the quick turnaround times for those I
think is is difficult. Um and it's that
is you know it shows a good thing if a
company is respectful of your time as
well. Yeah. Absolutely. So on the
behavioral side kind of a similar
question. What do you look for in that?
I mean obviously we talked about trust a
little bit before. Um we talked about
those communication skills. Is there
anything beyond those things that you
think you know is like you you take
mental note of?
Yeah. Um you know as objective as I try
to be humans are inherently subjective.
So what I have noticed is that I find
myself a lot of times feeling better um
about candidates in the like uh in this
part of the interview when they
are like confident and can carry a
conversation and
um speak about things but not
be, you know, not be rambling or not be,
you know, too
uh kind of you know like lost in
conversation. It's it's again this is a
very like human subjective part of the
interview and it's one that I don't
typically like not because I don't like
talking to people but just because I'm
so aware of the inherent like bias and
subjectivity of the whole process.
The advice that I would give is
like number one like be yourself, you
know.
Um the only time I would I would deviate
from that is if you is if you are truly
in need of a job and you know that you
need to sort of you know alter your
behavior for to keep yourself out of
deep financial trouble or something like
that. But you should be yourself. Don't
lie. And you should um you should go
into it willing to learn
um and treat it as like a learning
experience. I've noticed from my from my
personal experiences not uh interviewing
for a role is that if I do that, I end
up being a lot less nervous. Um, and I'm
I'll much easier to be myself if I treat
it as like I'm going to just go in and
whatever happens happens and I'm going
to just try and learn from this. And if
I treat it that way, I find that I
typically do a lot better. And then from
the other side, being the person that's
interviewing someone, I again, it goes
back to this whole thing of trust. like
I just want to know that this person is
going to be able to do the job that that
we have in front of us. Like you know,
we have this job, we have this problem
or we have, you know, we have this
client we need to work with. Is this
person going to be able to do it? If I
think that they're going to be able to
do
it, we're good to go. But if I don't
then, you know, um then it's going to be
harder, you know, for them to sort of
convince me otherwise later in the
interview process. So, it's all about
building trust and rapport with the
person. Well, this is where
communication skills can be extremely
important. I I couldn't agree more. And
you know, you touched something on the
confident side and that's something that
I don't think I explicitly address in
the course. And I think something that
can help people with that is whenever I
go into these. I also think of it a
little bit like I'm also interviewing
them. And it's not antagonizing in any
way. It's it's that hey, you know, I
know that my skills are valuable. I want
to make sure that this is a good fit for
me. maybe this isn't like the best
approach if this is your like um you
know your first you know you're really
hurting for that job like you were
describing but you know coming in with
that attitude one makes you more
confident because y if you're
interviewing someone else you have
confidence in yourself that you know you
could earn this role and two it also
helps to kind of expand conversation
right if you're asking really good
questions about the company about maybe
the the mission of the company some of
these things that looks so good for you
because you know it means that you're
genuinely interested. You want to find
out as much about them as you. And that
that kind of really can help build that
that connection and bring up talking
points that you wouldn't, you know,
necessarily have if it was just going
one direction. I thought of two more
things. I agree with you on that. Um,
one is something that I learned very
quickly and that I now look for is um,
when someone does research on our
company or you know anything that they
can find out about us that tells me a
whole lot about their priorities and it
also lends to a better like interview
and conversation. So whenever I'm
interviewing or going into an interview
I do as much research as I can. I find
out who I'm interviewing with. I look at
their LinkedIn profile. I go down a
hole. Yeah. I just go crazy and learn
everything I can about everyone at the
company, what they're doing,
initiatives, like I look at their about
page. I just everything. Um, the other
thing that I'll say is like
um, make your resume truthful and
um, I find that if I'm going in and I if
I am the subject matter expert on my
resume and my life and my past career,
no one's going to know it better than
me. Like, no one is going to know how to
talk about these things better than
myself. So, just be truthful about it.
and it takes a lot of the nerves off and
you can tell when a candidate really
knows their resume like they are just on
it and it makes you trust them. So I
think that's an important little tip
too. Yeah. Well, you know that that's
again one of the things that I really
recommend even in the course is that
know your resume cold. Have have the
stories about each of your positions
that you can tell that you can kind of
match these different questions. Right?
It's that y hey like you know when is
the time that you maybe had to deal with
a work with a co-orker that where you
didn't see eye to eye oh I did that at
this job because you know your resume so
well you can just go out and and kind of
knock those out. So absolutely you know
the best thing you could do if you're
not sure about your your own experience
how are they going to be sure about it
you know exactly
um I think you know I'd love to end on
any additional things throughout the
whole kind of data science job
experience that that you might recommend
any tips and also if there's anything
any projects you're working on anything
that you'd like to share I think that
this is a great time to be able to to
expand on that. Yeah, absolutely. So, um
I maybe just one or two things. Um one
thing that I would like to say is that
um you
know the direction that like data
science is going. I think there is often
times
and I think um in the online communities
like when you read medium and like you
know you other this not you can of
course um but when you read a lot about
these projects like there's a lot of
emphasis on
um like knowing a specific algorithm or
like you know really kind of almost I
would say like oddly niche pieces
of of experience. And while that is
valuable and you will need to know
it, nine times out of 10, at least in
data science
positions, excuse me, really the most
important skills are communication,
um the ability to know the the correct
solution to use, uh and how to identify
a correct solution.
um not necessarily knowing the ins and
outs of how um you know a certain
algorithm works. Um and I think in you
know you're going to be a lot more
fruitful in your career if that's the
case. Now there are exceptions to this
rule like there are more research or
algorithmic focused careers in data
science like machine learning engineers
and like deep learning engineers and you
know that's a little bit of a different
thing.
Um but like nine times out of ten you
know the product team you know at a
company is not coming to you and saying
we need a neural net for this design a
neural net like they're coming to you
and saying hey we have this problem um
we can't get user you know we need to
connect with our users more we have a
bad relationship with our users you're a
data scientist what can we do you got to
come up with the solution not just the
algorithm you got to come up with how
you're going to implement this, right?
So, you know, I think having a holistic
picture of how data science works in an
organization and in a business is is
like the most valuable thing because at
the end of the day, the CEO and you
know, the CFO and you know, all the all
the
executives, what they care about is
results. They don't care whether it was
boosted trees or you know like a
regression or whatever. They don't care.
They care about results.
So I would say be careful about getting
caught in the trap of like I need to
understand absolutely everything about
every single one of these algorithms and
be able to spout it off at the instant
that I'm asked about it. I think while
that's good, there may be better uses of
your time in learning how holistic
solutions are implemented um and how to
work with others. Um I see that so often
in resumeumés where they put like I used
this methodology and then they like go
on forever about it. You know, one of
the things that I really focus on is
like what was the outcome? Can you
quantify it? and then maybe we'll care
about what the how how you did it and
like yes, you know, if if the people
watching this are looking for a very
specific place to implement that. I
think the resume and also when you're
telling the stories about the your past
work experience during either the
behavioral or or some of the technical
parts um that is really where you can
apply that insight that Jefferson had is
that you know what what value can you
drive? How do we best talk about that?
talk about it by like one quantifying it
and two by like talking about it first
rather than how we did it.
Well said Ken. You said it
first. Um yeah, I mean I'd say that's um
that sums it up well. Um,
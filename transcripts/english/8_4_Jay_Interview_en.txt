Video Title: 8_4_Jay_Interview
Video ID: 2GRfhZLmG4s
Language: English (en)
--------------------------------------------------

Hello everyone. Welcome back to another
interview for my course, How to Start a
Career in Data Science. Today I have
another very special guest, <PERSON>. He has
worked in a couple different startups as
a data science out in the San Francisco
Bay area and he's also one of the
co-founders of Interview Query that
helps data scientists find their dream
jobs. So, thank you so much again for
coming on, <PERSON>. I'd love to hear, you
know, for our viewers just a little bit
more about yourself, your journey, and
maybe what got you interested in data
science to start with. Yeah, definitely.
Uh, thanks for having me, <PERSON>. Um, yeah,
so as <PERSON> said, my name is <PERSON>. I
started, uh, out in data science, um,
about five years ago, actually. I, uh,
began the journey actually in, uh,
Seattle as my senior year of college.
And so uh I was studying electrical
engineering at the time. Uh knew I hated
it when I was like working with
oscilloscopes and like printed circuit
boards and just could not deal with
wires anymore, right? And so I decided
to uh kind of pivot my um internal
studies uh and just like what I focused
on to data science while still finishing
out my major. And so I did a couple of
data science projects uh really like got
deep into them. U one was about uh
housing in Seattle in the rental market
back in 2015 and another one was about
crime as well. And uh one of those blog
posts actually made the local news. And
so um after that it was like actually
pretty easy to get job offers uh from
that point of uh just getting inbound
from different people. And so I actually
did then took a job uh at a startup in
Silicon Valley. uh left that after a few
months uh and then joined as machine
learning engineer at Jobber where we
eventually got acquired by um this
company called Monster uh one of the big
job boards uh back in the day and then I
worked there for about two years before
um joining Next Door uh which is like a
neighborhood social lab working there in
analytics for a year before uh currently
co-founding interview query which is uh
as Ken said like a basically a website
that helps data scientists prepare for
interviews at different startups and uh
bigger companies and then helps them
land their jobs once they pass an
interview. That's awesome. That that was
a really interesting story especially
because of how you got your first job.
You know, I think that uh one of the
things that I stress in the course is
how important projects are and why you
know the biggest reason you should share
them is because you never know what
could happen. You know, the news might
pick it up.
you know, a company might see that and
then that changes the whole paradigm.
Companies are coming to you rather than
you necessarily always knocking on their
door and there's so much there's so much
power uh for you in that scenario. Um
you know from your experience obviously
you've you're interacting with hiring
managers you're you're really
understanding how this interview process
works. What do you think the most
important skill is for a data scientist
to have, you know, in 2020 going
forward?
Yeah, I would say uh the biggest skill
I've seen um is kind of depends on what
the role is, right? And so because data
scientist is now being uh branded into
like two distinct buckets of either more
analytics focused or more um I'd say
machine learning software engineering
focused. Uh I think it kind of depends
on which one. Uh for the analytics side,
I'd say communication is probably one of
the um biggest uh like skills that you
can have uh that is severely downplayed
just because of the fact that most of
the time uh when you're doing analytics,
a lot of it uh half of it is
communication and then the other half is
actually like digging into the data. And
so, um, if you can't communicate your
insights to other people on the team or,
um, you know, like an executive team,
then you're effectively worthless,
right? Like there's literally no reason
for them to hire you. Uh, and so I think
that's like the biggest bottleneck. I
mean, um, if you have it the other way
around, obviously that doesn't work
either, where you have communication,
but you can't do any analytics. But at
the same time, um I think um what we've
seen is that there's a ton of people
that can do analytics uh and they can
write SQL queries, they can do analysis
in Python. Um you know, there's all
these courses to help you learn that. Uh
but I think communication is one of
those things that takes a lot of
experience, right? Um on the machine
learning side and software engineering
side, I would say a lot more of that is
around um understanding uh how like
systems work and how uh general
infrastructure works as well. So that's
like a key skill also is really really
hard to get and I think one of the
reasons why a lot of these machine
learning engineers get paid a lot
because uh they learn out how to like
scale out a system from like you know
let's say a thousand users to millions
of users and uh if you think about how
much more money that makes for a company
that's probably like 100xfold right and
so you're thinking about like literally
being able to help a company grow 100x
um and be able to maintain like a
website for that and I think uh because
so many of these websites and like
products use machine learning um being
able to like understand how to do that
uh from the software engineering machine
learning side will be uh is basically
like the number one uh kind of skill set
that most um companies are looking for
and then they'll pay a lot more for um
as you gain experience. Awesome. So a
little curveball here. So you mentioned
obviously the communication skill and
also understanding of systems or I guess
technically that's also like
understanding pipelines as well. Yeah.
Are there any you know concrete ways
that are pretty quick that you think
someone can improve either the
communication skills or the
understanding of the system
architecture?
Yeah, definitely. I'd say that in terms
of communication
um I'd say if you can't do it at your
like current job almost in terms of uh
you know if you're already like working
as in like uh analytics or like as an
analyst um in terms of like
communicating out like doing data
visualization and like talking to
different stakeholders then I'd say um
one pretty easy way to get started in
that is just uh writing blog posts about
your data science projects um it helps
you like effectively figure out what you
want to communicate and writing is
essentially like a form of communication
right except that you just think down
and you structure it the way that you
want to so I'd say that if you can do
the uh writing portion and figure out
what are like you know the key points of
your analysis what are like the specific
graphs that actually matter um how you
can communicate your insights uh to
readers and actually engage them um
you'll find it like as basically a
pretty good proxy towards how you can
communicate on the job, right? Be
because um I mean most of the time when
you're communicating on the job, it's
also reports, right? You're writing
reports, you're presenting your
findings. And so these things are I
think very very similar. And I'd say
that um it's very underutilized right
now um just because uh people don't
think to um really like write about
their project after they do it because
all the fun stuff was in the project. Um
and so I think that's like just an easy
way to exercise it, right? is just to
like practice that skill of just
communicating out your project um
because that's ultimately what helps you
um demonstrate your skill of
communication while also um reaching
like a broader audience in terms of
marketing it. Uh in terms of systems and
pipelines, I'd say building out um a
full stack data science project is
probably pretty interesting. I think
that would definitely draw people in as
well. Um and that means um being able to
understand like every uh part of the
process. Um and so in terms of scaling
that's almost um that's pretty much
impossible, right? Unless you have like
uh some sort of like personal example
where you can like serve like millions
of users. U but you know, no one's
really expecting you to be able to do
that anyway like in the very beginning.
And so I'd say just being able to design
um you know a scraper that can scrape a
website uh and then it can pipe that
data into a database and then it can
transform that data into a um a graph or
like a dashboard that already is um you
know three pretty important steps that
happens in the data science process and
I'd say that being able to do that uh
does then showcase your um kind of like
full analysis and full understanding of
data as a whole. Awesome. And you know
to to both of those points. So I know
Jay is is a writer on Medium. I have
also written quite a few Medium
articles. So people are welcome to check
those out. I mean a blog post doesn't
have to be anything too complex. It
doesn't even have to be a technical
analysis. Just understanding how to
write, how to structure an argument, how
to present information to people. Uh
there's probably some pretty good
examples on either his or or my Medium
page. So, uh, you I think you can just
search for either of our names on Medium
and it and it should pop up. So, yeah.
Um, so awesome. That that's really
really valuable advice. You know, one
thing I actually don't cover that much
on the course is is um building like
database infrastructure and creating
that pipeline. And that's something
that, you know, I've even omitted and it
could be very very valuable in your
projects. So, you know, we're talking
about how to prepare for the interview
process and, you know, actually going
through the interview process. What are
some things that, you know, when you
were doing this five years ago, you'd
wish you'd known beforehand?
Yeah, that's a good question. Um, I
would say that, uh, one of the things
that I wish I knew beforehand was how
important it was to, um, prior
prioritize effectively. And so I'd say
um once you actually get your first job
uh a lot of that learning curve is about
understanding um how a data science team
functions and how you can like
contribute to a data science team. Um,
and if you don't have like a team, if
you're just like in a startup and you're
like one of a few data scientists, then
it's more about like how you can um
effectively like move the needle as a
business um versus just, you know,
working on um whatever that you think is
like uh easiest or maybe most important
in your mind, right? Um and I say like
that contributes to just uh
prioritization of projects and
understanding uh where the value ad is,
right? Um and so I think as data
scientists, you know, a lot of our
responsibility is being able to make a
lot of these decisions and inform people
of decisions of what they should make um
in a business. And so if there is a
project that you know um will uh that is
available and that could make you know
the the business a lot more money uh
let's say you build like a classifier to
um I don't know for example like for
Facebook like rerank the newsfeed or
something and you know that that'll make
you know 10% more revenue uh then that's
something that you should bring up right
and that's sort of like the kind of uh
intuition that doesn't really come
naturally I'd say but uh you get as you
learn how uh these kind of like
businesses function and you learn about
like when you get more and more data and
you learn about the availability of
data. Um I'd say like when you start out
when you're ramping up it's very very um
overwhelming because you're basically
given like an entire um product plus
having to know all the technical specs
like below it, right? like you're
expected to know how the business works,
how every like uh thing functions within
like a specific feature or team that
you're on and then also know where like
the data exists so that you can like
write queries to get it. And I'd say
like once you actually start ramping up
then your value ad becomes like when you
actually figure out what you can do to
like drive the business forward from all
the data that you now have knowledge to
because a lot of the times engineers
don't have any idea of like what kind of
insights they're trying to prepare
because they're you know uh busy
building the product. uh product
managers don't know what kind of data
kind exists uh that you can actually
dive in and do and like they don't know
a lot of the fundamentals of machine
learning that could actually actively
help uh the business for like a new
feature and so that's kind of where the
data scientist comes in and uh tries to
almost do like part you know maybe part
product management at sometimes maybe
part uh machine learning software
engineering at sometimes when in terms
of building and communicating with
engineers uh and it's all about kind
like knowing the ins and outs uh in
terms of prioritization at that point.
Awesome stuff. And I I guess you know I
might be jumping around a little bit but
one thing I I really wanted to get your
take on is you know what are a few
things that a data science candidate can
do to differentiate themselves you know
before the interview process starts
even. Yeah definitely. Uh I think in
terms of differentiation, I think
understanding the business very well is
uh what I would say would be like the
most differentiable at this point in
time at least because just because I
think that a lot of people believe that
data science is mostly um you know
building models or like uh I don't know
running SQL queries or something like
that. Technical basically. Yeah. Yeah.
Very technical. Exactly. And I'd say
that uh if you can just further um build
out like the more product intuition
side, the kind of business side, that
definitely gives you an advantage uh
when talking to um the different uh I'd
say like engineers um or non-engineering
folks uh such as like product management
and the executive team just because um
you know you don't want to come in kind
of blind into an interview when they're
gonna probably ask you um specific case
questions around like the product as
well, right? Um if you come into like an
interview with uh Google and you don't
know how like you know, I don't know,
Google Maps works or something like that
or you haven't thought about it or used
any of their products, then you know,
you're not going to do very well, right?
And so just having that idea of the
framework um and the context around
their products uh helps a lot when they
just ask you general questions like can
you predict like the estimated time of
arrival uh for um this like Google maps
like feature right and so just having
that like kind of background really does
help. Well, you know, one question a
long time ago that I was asked, the
company just was like, "What does our
business do?" And like obviously
businesses are are fairly complex, but
yeah, you should ask yourself that
question because once you start going
you start pulling the thread, it really
unravels like all of the different
elements and how they're tied together.
So, I think that, you know, doing your
homework and like really researching the
company, being able to answer that
simple question is something that could
hopefully help help you actually like be
able to understand the nuance of how
even want to, you know, like to ask what
Google does in this day and age. It's
like, well, you do everything. I guess
in theory, you sell a lot of ads. But,
um, but, you know, that is something
that's so powerful and and so many
people just completely overlook that
aspect. Yeah.
So, let's just move on to some of the
interview process. You know, I'm really
interested in, you know, what people are
looking for in the technical interview
versus the behavioral interview. I mean,
at at a high level, it seems pretty
obvious. It's like, oh, you know, we
want to see if they have technical
skills and we want to see if they're a
fit. But, you know, peeling a layer back
from that, you know, what are companies
really looking for there?
Yeah, I'd say uh for the technical
interview, a lot of it is um I think
about uh passing like specific
baselines. Um being able to uh not be
like caught into like their red flags.
And so a lot of the times, you know,
companies will just have like a general
level of what you should kind of pass
for a technical bar. Um and it usually
happens in the technical screen. And so
this is kind of like a baseline level
knowledge of um Python um maybe
statistics maybe machine machine
learning but I think a lot of that is
kind of around uh screening out
candidates that either don't have enough
experience or like clearly don't um know
you know what they're talking about in
terms of uh those skills right uh and
then after that I think it's more about
um trying to understand kind of a couple
of different aspects of data science
whether that's like product intu
intuition um whether that's like your
kind of your analysis skills um and then
maybe further kind of if if it's more on
the machine learning side then more of
that like engineering implementation
kind of uh kind of task and so I'd say
um one of the uh kind of things that I
think most likely happens on an
interview but it kind of it really
depends right because uh interviews are
so varied is based on um how well you
can kind of like learn within the
context text of the problem. And so what
a lot of uh interviewers want to see is
just essentially like how well can you
um learn or like how well can you debug
uh how well can you kind of like get
yourself back and um improve as you go
through this interview. Uh because a lot
of it is about how quickly you can ramp
up on the job, I'd say. And so being
able to demonstrate that you can um
adapt within like a technical problem I
think is uh one of the the key skills uh
that isn't really talked about much um
and isn't really easy to describe either
I'd say uh but I think it is something
that like uh is super important and it's
hard to um really practice uh except if
you like continue to do like practice
problems where you're faced with
different scenarios in different
situations uh and then being able to uh
come up with like a solution from that.
Awesome. And so moving on to the
behavioral interview, what what are
companies really looking for there?
Yeah, I'd say uh for the behavioral
interview, companies are definitely
looking for I think someone that fits
their core values. Uh, and so I think
each company generally has um, uh, kind
of like missiondriven core values for
what they want their uh, their employees
to be like. And so a lot of this stuff
uh, it can be like very different,
right? Um, there's like companies that
just want um, people that are easy to
communicate with, uh, that, you know,
they'd have a good time outside of a
work with as well. um expect to be
friends when you're like sitting in like
eight hour capacities with each other
each day. Um and then there's more like
companies that are focused more on like
solely maybe your skill set, like maybe
your just like drive for motivation, uh
your willingness to like work through
challenges even if they don't appeal to
you. Um and these aren't exactly
contradictory, but they're just examples
of different values that I think um
companies are shooting for, right? And
so I think uh for a lot of the Hayroll
interview, it's kind of about matching
up that fit and like understanding what
uh your values are and what you'd kind
of be like as a uh contributor on the
team. And uh if you know if there isn't
really that if you guys are completely
misaligned, then I think that should
inform the candidate as well in the fact
that uh maybe this is not a place that I
would like to work at or that I could
succeed very well. But I think um you
know at its core it's all about like can
this candidate you know succeed in the
environment that we're going to place
them in uh if so uh this is how we're
going to kind of like ask the questions
to understand that impact and I think
it's important to note that you know
most companies on their website have a
mission statement that's a place that
you can find out some of this stuff
there there's websites like glass
door.com where if you read reviews you
can get a pretty good idea about like
the type of uh the type of person that
would fit well there. You know, an
example is that like Netflix has a very
much like you work hard, it's up or out
culture and like that might work really
well for some people, but for other
people that might be absolutely
terrifying. So, I mean, I would imagine
from reading Netflix data science uh
reviews, you'd be able to figure that
out pretty quickly. Yeah. Yeah. It's
it's hard, right? because I think glass
door definitely skews onto like a
person's willingness to you know jump in
there and I think Netflix c like their
core culture is uh really great they
have this whole culture deck you know
that everyone knows about um and I think
uh as long as they follow to it and they
um transparent about it then you can't
really argue um when you you know don't
like it or you get fired for some
culture reason or anything because a lot
of of the times it's that's basically
what they said um going forward and
that's kind of what you're agreeing to
when you sign like the offer letter or
you go through the interview process as
well. Awesome. And so you know one last
uh question that I have is that you know
my my background I've worked in the
Midwest and on the East Coast. I don't
have as much experience interviewing in
startups or at some of like the really
tech forward fang companies. Do you have
any advice specifically related to
either one of those groups, the startups
or the fang companies that you know
people should watch out for or pay a
particular attention to compared to just
any old regular data science job?
Yeah. Um I would say that so the number
one thing that I think is the most
differentiated is that they have a more
standardized uh process for data science
hiring. uh at Fang. Um startups can be a
little bit hit or miss. Uh but I'd say
that Fang uh has this uh sort of like uh
standardized rubric for every single
candidate. Um you have to pass these
things. uh you have to demonstrate you
know these core values and es especially
I think for um some of the bigger
companies like Facebook and Amazon
Google they basically know uh generally
like what is passing level for um every
single one of their uh questions and
that's because they've been um they're
very data driven companies at its core
and so the data scientists that make
these tests uh make these interview
questions have this uh almost like idea
of like where you fall on the
distribution compared to all the other
candidates that have taken these
interviews, right? And so, um, because
they've kind of figured out hiring this
way and they've gone, uh, such a huge
volume, they've gone this huge data set
of just general, um, you know, interview
data, uh, and that's kind of how they
you uh, use that to select who they're
going to hire, uh, and bring on uh, for
interviewing. And so I think a lot of
the tips around that is around
practicing problems that are similar to
like what you see on those interviews,
being able to like understand what the
hiring process is like, talking people
at those companies or like friends or
reaching out. Um maybe meeting coaches,
stuff like that and just that understand
that whole process. Uh that can kind of
simplify it down for you. Um and just
getting used to like that whole
situation. Uh I think startups one of
the key things uh at least is uh the
advent of like take-h home challenges
which I'm personally not like that big
of a fan of but I understand uh why they
do it. Um and it's because of the fact
that uh they standardize those uh
because it's helpful for them to get
like an idea of how the candidate might
actually do in terms of an actual work
environment. And so a lot of the time
startups will have very specific tasks
for data scientists um and it'll be very
specific to their business. And so
they'll give like an example of a data
set or like a business problem that is
uh specifically focused around their um
actual startup. And I think that's kind
of how they do a lot of the data science
interviewing because uh it is more
specific to a startup. they don't have
all that data um there in terms of being
able to measure candidates and then it's
also probably heavily dependent upon who
the existing data scientist or manager
is uh at that startup as well uh and
kind of like what they think is uh a
good bar for hiring for their team.
Awesome stuff. I that's really it for
all the questions I had. Do you have
anything you'd like to add? Any any last
words of wisdom? Any plug or anything
like that? Yeah, I mean uh please check
out uh interview query. It's a website
that uh me and my co-founder developed
in terms of being able to uh really
emulate like the interview uh technical
questions I'd say. Uh in terms of
behavioral uh that's I think a little
bit more broad uh and you know a little
bit easier to uh find elsewhere on the
internet. But I think for us we really
wanted to figure out uh what the
questions were being asked at these
companies and be able to create them in
practice problems uh that you can find
uh and so yeah you can access it um on
interviewer.com and then just check out
the problems and join the discussion
with other data scientists.
Video Title: 8_3_J<PERSON>min_Interview
Video ID: xbdWNHINQZ4
Language: English (en)
--------------------------------------------------

Perfect. Uh, well, we might as well get
started. So, <PERSON><PERSON>, thank you so much
for agreeing to to have me interview you
for this course. I know that you have
some really unique experience and I
think it would be really valuable for
everyone who's watching this when when
they're going about applying for jobs
and trying to break into this field. So,
for for everyone watching, <PERSON><PERSON>
recently graduated from college and he
just landed his first position in data
science. you know, uh, his background is
unique because he is an international
student and I think that that's a very
unique obstacle that that that some
people have to face during the hiring
process. Uh, he's going to talk a little
bit about that as well as a little bit
uh, and about his experience going
through the interviews, the resume
building, etc. So, <PERSON><PERSON>, why don't you,
you know, I introduced you a little bit,
but if you have anything to add to that,
I think everyone would value from a
little bit more about your story
relating to data science. Yeah,
definitely. Thanks for having me, <PERSON>.
Um, hello everyone. My name is <PERSON><PERSON>
and I'm originally from South Korea and
I recently graduate with the Bachelor of
Science and Data Science at the
University of Texas in Dallas. And um I
was originally a computer science major
and during my um junior year, first
semester of junior year in college, I
got into stock trading and that's where
I learned about u all those like
predictive an analytics and you know
using data to predict stock prices and
yeah that's how I got into data science
and luckily after the semester my
university actually announced that
they're going to have a data science
degree for undergraduate students and I
didn't hesitate to change my major from
CS data science. So, yeah. Um um and I
recently got hired at a professional um
services company. I'm not sure if I'm
supposed to um disclose it, so I'm not
going to say the company's totally fine.
Yeah. Yeah. And I will be starting
working in August. Um, I also started a
YouTube channel and uh where I'm going
to start um talking about data science
and some of the tips for current and
incoming international students here in
the US. So definitely feel free to check
them out. Yep. So it's import data. You
can go check that out. I I'll have some
links on my channel as well as some
other places for anyone want who wants
to learn a bit more about that. So you
know do you have uh like an early memory
of data science and from the stock
trading or anything like that? What was
your first kind of um you know
experience with the field that either
made you like really interested or made
you a little bit maybe scared of it? Oh,
um I I really like the idea of just you
know using data to predict stuff like I
just didn't know about it and you know I
started all those um variables that that
can like affect stock prices and that
was really fascinating to me and I mean
I didn't really study that much. I just
have like basic ideas of like variables.
So yeah. Very cool. So, I'd love to hear
about your interview process, you know,
going in, you know, perhaps how many
companies you applied to or how many,
you know, interviews, what your funnel
looks like to actually eventually land a
job, right? Yeah. So, um I applied to a
little over a little more than two uh
200 companies over four months. Yeah.
Um, as I mentioned before, I'm an
international student and international
students only have a certain amount of
time to land the job and otherwise
they're going to get basically get
kicked out. You have to go back to your
home country. So, um, yeah, I was
desperate. Um, so I wanted to apply as
many job as many job as I can and I only
got about five five callbacks out of
those 200 companies and I ended up
getting three interviews and out of
those three interviews I ended up
getting the position where I applied
through referrals. So turns out using
your network and um using referrals are
the best ways to land a job. Awesome.
You know, that's something I I really
stress in the course is that, you know,
this is a low percentage game if you go
about it in the traditional way and you
can really really increase your chances
through through the referrals and those
types of things. So, I'd love to hear
more about your interview process. Um,
and you know, what what types of of not
necessarily questions, but
um what were the phases of the
interviews? You know, did you have a lot
of technical phone screens? Did you have
behavioral interviews? Did you have to
do SATS coding questions? What did that
look like for for most of the of the
five interviews that you did? Right. So,
um so the company that I got the
position from, I I only had technical
interviews and like behavior type of
questions interviews. I didn't really
have any like um you know coding coding
homework or anything like that. But I
did have did have some of the coding um
homeworks from other companies that I
applied to where I had to do the data
analysis basically. Um but yeah uh from
the company I got the position I only
had um behavioral and the technical.
Awesome. And you know, I I think that
that speaks to how different a lot of
these companies can be and how um you
know, did you use any tools like glass
door, things like that to research what
types of questions you might be asked
or? Yeah, I use glass door. Glass door.
Yeah, I use glass door. Awesome. Well,
again, that's something I really
recommend to everyone. I go through
glass door. If if you did get referred,
you should definitely ask anyone who
referred you for any tips, things like
that. it's in their best interest to uh
to help you get that job. Yeah,
definitely helps. Right. Totally agreed.
Is there is there any other uh you know
parts of being an international student
that um you know that were important
during the interview process that you
want to touch on just a little bit more?
Right. So, um, uh, when when when you
see the job descriptions, not a lot of
companies actually list that, oh, they
only hire international or, uh, US
citizens or green card holders. So, I
actually had to ask and make sure that
they were okay with me being
international students and with my
status and that and that they are hiring
international students. So, that was one
of the most important things that I had
to go through in the beginning. Awesome.
Well, you know, I mean, that's something
again that I think a good portion of
people who are who are going to be using
this course, viewing it are going to be
thinking about um might be
international. And so I I think that
that's really important because you
don't want to go too far along the road
and then be like, we can't actually hire
you. Yeah. Then
um so you know what what do you think
that the most important part or the most
important thing that you did was uh that
helped you to get one of these roles?
maybe maybe outside of going the
referral route and the networking, you
know, what what can someone who is
trying to get these jobs do that they
have control over to to put their best
foot forward?
Um, so there are definitely many advices
I can give. Um, I I think I have three
major ones. The first one is um to do as
many projects as you can. um because um
you'll get used to the the technical
interview questions as you do. You
you'll naturally know what type of
question they're going to ask because
you're going to have to use certain
algorithms or whatnot. And the second
one is to build a personal brand. So you
don't have to start big. you can just um
put your code on GitHub or start writing
blog posts on Medium and make like
personal website and explain what you
did uh during your um uh projects and
yeah the third I guess the third one is
to use referrals which we basically
talked about.
Very cool. I think that those are all
like really really awesome pieces of
advice and obviously talk about those
quite a bit in the course. So if you
need some refreshers, definitely check
out those those sections. Now, you know,
you talked about the value of projects.
What did your projects look like? Can
you speak a little bit about some of the
things you did? I I get a lot of people
asking for project ideas. I don't think
that they you should they should copy
your projects, but the more they hear,
the more they might think outside of the
box about what they could actually
analyze, right? So, it's funny how
because like I actually asked you can um
before I started applying for position
like what type of um products I should
have on my resume and then you specified
to have regression clustering
classifications and deep deep learning
if possible and luckily two semester ago
which was um I was about to apply for
positions in data science I took machine
learning and deep learning courses so
that was definitely helpful. Nice. And
yeah, and um I did a personal project
using deep learning on identifying uh
dog breeds. And I think it's important
to do like to show or to put one or two
personal projects on your resume that to
show that you're really interested in
this
field because that can probably separate
you from other candidates. Yeah. You
know,
that's some people don't really like
that aspect of the interviewing that
like, you know, they think that you
should have your your work and then your
life and you can have those separate.
You know, regardless of if that's true,
I think that companies still view
someone that's willing to put their own
personal time towards learning data
science, towards these projects as more
positive than the than the latter. And
you could take that as a good thing or a
bad thing. I personally don't think I I
would have been successful as a data
scientist if I didn't really love what I
was doing and I like didn't really have
the ability to turn it off as well. So,
um you know, as as you went through, you
know, the projects and the learning and
then onto the interview, what do you
think your best piece of interviewing
advice is, um for for anyone who's about
to start that process, whether whether
it's the phone interview, the technical
interview, or the behavioral interview.
um you know what do you recommend for
someone there?
Okay. So, uh my best piece of advice is
to find someone who can help you or I
guess find a mentor because um luckily I
was surrounded by many individuals who
um helped me prepare for the interviews
and obviously you Ken is one of them and
yeah I had a pretty good understanding
of um what I need to what I needed to
prepare for um technical interviews and
behavior interviews and um there might
be there may be your family members or
alumni who are already in the industry
or YouTubers or blog uh bloggers who
really who talk about inform uh
informative information out there. So I
suggest to just find someone that you
can really benchmark and um go from
there. Awesome. And you know I think
that it is important to note that it
doesn't necessarily have to be someone
that's like familiar with all the
technical details of the of the field.
you know, it could be your girlfriend,
it could be one of your friends. Just
talking about the concept and getting
them, someone who's not familiar with
the field to be able to understand it,
um, is just as powerful because in data
science, in the interview process, most
of the time you want to take complex
things and make them sound very simple.
So, I I I I agree that, you know, just
practice repetition, whether it's an
expert data scientist or it's your
little sister, you know, there's still
value that you can create uh, through
all of that. And so definitely, you
know, I think my last question here, I'd
really like to know if there's anything
that you wish um you knew before going
in. And also, you know, kind of on the
best interview advice type of uh concept
as well, just like what's your best
recommendation for anyone taking this
course or trying to break into the
field?
Right. So,
um let's see. I would say um I I know I
mentioned this earlier but I would say
just do as much as many projects as you
can because uh like I said before you
will naturally learn the concepts and
you'll naturally be prepared for the
technical interviews. So that way you
don't really have to, you know, prepare
for technical parts of your interview
process. And um like I said earlier,
find someone who can really um guide you
in the right direction. Um it doesn't
have to be someone you really close to
like Ken said. Um you know, it can be um
YouTubers, bloggers, and you can maybe
um reach out to them. And that's how I
approached Ken in the beginning and he
was very nice about it. So yeah, those
are the two um advices or advices I can
give. Awesome. We're trying to Yeah. get
into this field. I I'm going to stress
again the projects. I mean, that's
something that was really important to
you. Um right, you know, there's there's
projects that you can, you know, I show
you how to set up projects in this
course on Kaggle and on GitHub. You can
also see some of the projects that I've
done on my YouTube and and how I go
through each step of those things. Jamon
on his his channel as well has quite a
few projects and I I think that you know
you don't even have to necessarily
um speak one-on-one with someone to be
able to get value out of out of the work
that they're doing. There's a lot of
people through blogging, through Medium,
through YouTube, through a lot of these
platforms. Um, exactly. That that have
great information that's out there uh
for free and and you know, you can use
those people as mentors. You can use uh
their work uh to be able to see what's
going on and what the expectations are.
So, thank you so much again. Is do you
have any final comments that that you'd
like to make? any any last uh last words
of wisdom for for the viewers or
uh yeah I know data science might be
daunting for you because there's a lot
of things that you have to do but I
would say um just start small don't try
to do everything at once and yeah just
do as many projects as you can
and you'll get eventually get There.
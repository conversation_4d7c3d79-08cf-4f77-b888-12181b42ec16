Video Title: 8_9_Bonus_Content_Portfolio_Website
Video ID: JgiWCoFgR2M
Language: English (en)
--------------------------------------------------

Hello everyone, Ken here back with
another video for you. Today I'm going
to show you how to build a data science
portfolio website using GitHub pages.
This is hosted online completely free by
GitHub. And I think that this is a great
way to showcase your work to potential
employers. This is what we're going to
be building today. It's going to look
just like this. Very simple as you can
see, but it shouldn't take you more than
10 or 15 minutes. Why would you want to
build a portfolio website like this?
First, it looks very professional. And
when employers are coming through
looking at your work, they're probably
not going to want to go straight to the
code or dig too far into the details.
This gives them a highle view about what
you're working on right now. This also
gives them the opportunity to maybe
drill down further if they see something
that they like or is really interesting
to them. Again, this is a very simple
version that shouldn't take you very
long to put together. All right, without
further ado, why don't I show you how to
build this out? So, this is again what
it will look like when we're finished.
This is GitHub pages. There's some
documentation for how this is done. I'll
link that in the description. We're also
going to want to just go to our our
GitHub here, and we're going to create a
new repo. So, I'm going to call this
Ken
Portfolio, and we're going to just write
a quick description here.
Great. So, we're going to initialize
this with a readme. Usually, I don't
initialize these with a readme because
I'm going to be connecting the
repository that I made locally to
GitHub. But, in this case, we're going
to do pretty much everything on GitHub
online. So, we won't have to worry about
that issue. So, we're going to create
this repository.
And when we build the GitHub hosted
website through GitHub pages, what's
going to be shown on the website is just
our readme. So we won't have to know any
HTML. We're just going to go through and
make the readme look really good. Add
links to it using markdown. So let's
open just a markdown cheat sheet. So we
have
it. I'll also link this below.
But now I'll show you how to actually
build out this website. So what we do is
you go to the settings up there. You
scroll down to GitHub pages. We're going
to connect this to the master
branch and momentarily we'll be able to
see what was on our readby on our readme
at this web address. So let's open that
in a new window. Right now it's not
found. It usually takes a couple seconds
to get up and running. So, let's just
kind of wait on that and start building
out our readme a little bit more. So,
the first thing we're going to want to
do
is Oops. Where is my
Oh. So, let's go back
here. So, the first thing we're going to
want to do is actually spice this up a
little bit. So, let's add in some of
our, you know, readmes from our other
repos. You've already hopefully done
this, so this should be really quick to
build out. So, we can just copy this
in to our new one. We'll edit
this. We're going to call this project
one.
And we can preview what this will look
like. So, it looks pretty
good. We're going to commit the changes
and then let's see if the website is
actually running yet. All right. So,
let's check this out. It looks like it
is running now. As we can see, our
project is there, which is really good.
What we want to do is actually add a
link to this. So, when we click this, it
actually goes to the repo
that that has all of the code for this.
So, let's go back here. We're going to
go just to this data science salary
project and we're going to copy the
link. In order to get a link using
markdown, we put the code, put the text
in brackets and then the link in
parenthesis like that. So, we're just
going to make that quick adjustment
here. So, we're going to put this in
brackets and then we're going to put the
link like
this
and it should be working now. So, we see
that the link works here. This goes to
that page. It should also work in our
web page once it is updated. Again,
these things take a little while to
update. So, just cuz it's not showing
does not mean it is not there. Oh, it'll
eventually work. I promise. So, the next
thing we want to do is let's add another
one of these, another project, and then
eventually a couple pictures to make
this thing pop a little bit. So, let's
use this this read me as well. This
project overview. We're going to
do this
code. going to copy it in here. We're
going to
change this to a proper header and we're
going to call this project
two to ball
image classifier. Again, we're going to
put this in
brackets and we're going to link to the
main page here.
Perfect. So
again, we're going to add project
two check that this
works. Okay, now let's again check our
portfolio. It looks like this one is
updated. So we can see that it links
correctly to the page. The other one,
again, whenever the page refreshes or
whenever it loads, that will be in
there. After that, we're going to want
to add a couple images to this to this
readme. So, as you can see in some of
the
uh in this readme example, for example,
we have this image uh these three
images. What you're going to want to do
is you're just going to go here and save
the image to your, you know, wherever it
is on your local computer.
uh we we can't do the images the same
way as we did in this repo where they're
just sitting in
um sitting in the repo and you link to
them. We're going to have to create a
special image folder in our webbased
repo here. So what we're going to do is
we're just going to go into into here.
We're going to create a new file called
images. And then we're you just put the
slash to make it um making a new thing.
And we're just going to add a readme
document here. So this is going to be
kind of a
placeholder, but we're going to upload a
couple of the image files that are
relevant here. So I've already
downloaded them. They're just these
two. And we're just going
to added
files. Great.
So the next thing we need to do is we
just have to link the images in here.
And we can use a lot of the same stuff
we did in in for example like this
readme. So we know the image looks like
this. We don't actually have any alt
text.
So we can just
um go in here. We can copy the image in.
We can remove the alt
text. And let's go figure out the
location of those images. So we can
click
here. And for the first one, it's
position by state. So we're going to
copy the link address. And then we are
going to just put that link address
right here. There we
go. Um let's do the same for the other
image, which we can find here. the
matrix results. Copy link address. And
then we put that down
there. Perfect.
All right. So now we can see in the read
me the images are in there. They look
pretty good. They should be showing in
our website now. So it looks like this
is still lagging a little bit
behind, but we see everything that we'd
really want to see there. You can put as
many projects in as you want. Again, I
generally recommend at least four where
you talk about classification,
regression, clustering, and then you do
some sort of deep learning or NLP or
computer vision
project. The more projects the better,
frankly, and the more interesting they
are and more the more unique they are,
the better. I have a ton of videos, an
actual playlist that I've linked above
and below where I talk about all the
intricacies of projects here. So, let's
try and refresh that one more time. It
looks like we're not getting any images
as of right now, but let's try and fool
around with some different formatting.
And I'll show you how to actually make
this look a little bit prettier using
some built-in themes. Again, this is
just supposed to be the simpler version.
In the next version of this video, I'll
be using some actual HTML. So, we're
going to go into back into this file.
We're going to go down here to settings
and we can choose a theme here. So, this
uses Jackal. Uh, let's just choose this
theme. It looks pretty good. And so,
it'll apply this theme to our readme up
there. So, we'll be able to see um like
that formatting attached to our little
page
here. All right. So, now you can see
that it's refreshed. This is how it
looks. It appears that we're still
having a little bit of issues with the
images. So, let's go back and try and
fix that. So, again, it seems that
they're showing pretty well
here. Um, let's see if we can get them
to actually show on our
page. All right. So, for the image issue
we're having here, it looks like I just
have to go in and change the path a
little bit. So, we're going to be using
a slightly different path than the whole
uh the whole thing here. We just have to
have the images like this and it should
still display clearly in both
places. So, we're still seeing them here
even with that adjustment. Again, it'll
probably take a little bit of time for
this to refresh and show up in our
actual web page, but as you can see,
this is pretty well styled already.
There's a lot of good features here. Um,
and this looks very professional. You
can convey exactly what information you
want to to have uh overall. So, you
know, obviously this took me, you know,
around 10 minutes. if you're not talking
through it, it'll probably even take you
a shorter amount of time. So, as I
refresh it there, we can now see that
these graphs are in there. Uh, if we
want to real quickly kind of turn off
the uh the formatting that we did, the
theme, it'll look almost exactly like
this on the web page. So, I hope that
this video shows you how to really
easily create a professionallook data
science portfolio page with really
little effort. I think that a portfolio
will help you stick out to employers and
this is something that will convey to
them exactly what you want them to see
in the fastest way possible. Again,
sifting through a GitHub profile can be
timeconuming, and this is a way to give
them a kind of highlight reel of the
things that you worked on or are working
Video Title: 2_4_How_to_Differentiate_Your_Projects
Video ID: 0LH9u0ktEv8
Language: English (en)
--------------------------------------------------

Most other data science applicants will
also be doing projects. If you want to
give yourself the best opportunity
possible for getting a job, you'll want
to start to differentiate your projects
from theirs. Here are some ways that you
can make your project stand out from the
pack. One, unique projects. If you're
tackling a problem that hasn't been
attempted before, this can carry a
tremendous amount of weight.
Additionally, if this project combines
your personal interests, that tells a
great story to prospective companies.
Two, your project creates value for
someone. If people are actively using
your code or the findings of your
project, this illustrates your
understanding of user and business
value. If you can do a project for
someone, maybe in the nonprofit area,
you're helping to solve real business
problems with data science. The third
way to differentiate is with data
collection. This is a way to flex your
web scraping ability. As a data
scientist, you may have to go out of
your way to get your data. Web scraping
or using unfamiliar APIs is a valuable
skill to showcase. This may add some
extra time to your project, but I truly
believe that it'll be worth it. Four,
your model is only as good as the data
that goes into it. Creating new features
for your model is a great way to improve
its performance. This is something I
personally look for in candidates. For
example, if you have latitude and
longitude data, you could create a new
feature that is a distance from a common
location. You could also get a rough
climate from these coordinates. These
could be extremely valuable for
predicting your outcomes. Five, when
solving a problem, you should try
multiple different models. Often, it's a
combination of these models that
produces the best results. I recommend
exploring how pairing models impacts
outcomes. These are known as composite
models or ensemble approaches. Six,
deploy your model. When you make your
work into an API or a web app, you turn
your project into something that is
truly tangible. Better yet, if a
prospective employer can interact with
your product via a website or an API,
this will definitely get their
attention. Seven, publish your work. If
you're in school or you have other
opportunities for published research, I
highly recommend pursuing them. Having
academic credentials is a badge of honor
in the data science community. Eight.
Finally, it's important to clearly
articulate the value you're providing in
your projects. In the next video, I'll
show you how to properly talk about your
projects online. You can either do this
in the code or in the readme file.
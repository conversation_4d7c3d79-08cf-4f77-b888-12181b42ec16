Video Title: 3_6_Resume_Checklist
Video ID: kxVwWO4rqRU
Language: English (en)
--------------------------------------------------

Some of this is review, but I want to
highlight a few specific concepts. This
can also work as a checklist for when
you're building or after you complete
your resume. First, make sure to include
links to your GitHub and your Kaggle
profile. You worked hard on those
projects, and you want recruiters and
managers to see your progress. Second,
customize your resume to a specific
position that you're applying for.
There's competition for each role, and
you want them to think that you are the
perfect candidate for the position.
Don't give them any reason not to take
you to the next round. Third, focus on
outcomes over algorithms. If the outcome
of your project is interesting, they'll
want to bring you in for an interview to
hear more about the algorithms and the
process that you used. This also shows
you understand how to drive business
value as a data scientist, which is a
vital skill. Fourth, use specific and
quantifiable descriptions for your
projects and your work. As data
scientists, we use numbers to tell a
story. And if you do this in your
resume, they know that you'll be able to
do it in an on the-job project. Fifth,
show don't tell. A recruiter should be
able to see your ability to focus your
leadership qualities and your
communication skills through your
project, your work experience, and
through some of the interview process.
There's no need to tell them this
outright in your skill section. Sixth,
add personality. You're more likely to
get hired if people like you or they
understand something about you. You
don't necessarily need to have an about
me section on your resume or anything,
but including volunteer work or clubs
that you were in in college can help
guide the conversation and lead to a
connection with your interviewers or
your peers. I had a friend who talked
for about 30 minutes with their
interviewer about the game Overwatch.
And guess what? He got the job. Seven,
avoid grammatical and context errors.
Having grammatical errors and improper
use of terms can be a surefire way to
get rejected. I recommend reviewing your
resume multiple times and having others
look at it to check for these problems.
I pay particular attention to this if
English is not your first language.
Eight, include project work. This is
especially true if you're lacking data
science experience. Again, this is the
best way to set yourself apart. If you
keep these ideas in mind when you're
building out your resume, you'll be
setting yourself up for success. In the
last lesson, I'd like to talk about the
cover letter. Some believe that it's
gone out of vogue in the last couple
years, but I still see it as a valuable
way to express your interest in a
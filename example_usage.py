#!/usr/bin/env python3
"""
Example usage of the YouTube Playlist Transcript Downloader.

This script demonstrates how to use the downloader programmatically
instead of with user input.
"""

from youtube_playlist_transcripts import YouTubePlaylistTranscriptDownloader

def main():
    """Example usage of the transcript downloader."""
    
    # Example playlist URL (replace with your own)
    playlist_url = "https://www.youtube.com/playlist?list=PLSCaSr0QLzkL3mo4iKIdkvheZUJzT2Bdq"
    
    # You can also use a playlist URL from a video:
    # playlist_url = "https://www.youtube.com/watch?v=VIDEO_ID&list=PLAYLIST_ID"
    
    try:
        # Create downloader instance
        # Note: Folders will be automatically named based on playlist title
        downloader = YouTubePlaylistTranscriptDownloader()

        # Download transcripts
        downloader.download_playlist_transcripts(playlist_url)

        print("✅ Download completed successfully!")
        print("📁 Check the 'transcripts/[playlist_name]/' folder for your files")

    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()

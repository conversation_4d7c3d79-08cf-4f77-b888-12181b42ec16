@echo off
REM Setup script for YouTube Playlist Transcript Downloader (Windows)

echo 🚀 Setting up YouTube Playlist Transcript Downloader...
echo ==================================================

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed. Please install Python 3.7+ first.
    pause
    exit /b 1
)

echo ✅ Python found
python --version

REM Install dependencies
echo 📦 Installing dependencies...
pip install -r requirements.txt

if %errorlevel% equ 0 (
    echo ✅ Dependencies installed successfully!
    echo.
    echo 🎬 Ready to use! Run the script with:
    echo    python youtube_playlist_transcripts.py
    echo.
    echo 📝 Features:
    echo    • Downloads English transcripts
    echo    • Auto-translates to French when needed
    echo    • Organizes files in language-specific folders
) else (
    echo ❌ Failed to install dependencies. Please check your internet connection and try again.
)

pause

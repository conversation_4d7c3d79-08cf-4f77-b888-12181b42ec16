#!/usr/bin/env python3
"""
Flexible YouTube Playlist Transcript Downloader

This version can handle any available language and translate it to English/French.
"""

import os
import re
import ssl
import sys
import time
import json
from pathlib import Path
from typing import List, Optional, Tuple

try:
    import yt_dlp
    from youtube_transcript_api import YouTubeTranscriptApi, NoTranscriptFound, TranscriptsDisabled
    from deep_translator import GoogleTranslator
except ImportError as e:
    print(f"Error: Missing required library. {e}")
    print("Please install required packages with:")
    print("pip install yt-dlp youtube-transcript-api deep-translator")
    sys.exit(1)

class FlexibleTranscriptDownloader:
    """Downloads transcripts in any available language and translates to English/French."""
    
    def __init__(self):
        # Configure SSL for macOS
        self._configure_ssl()
        
        # Configure yt-dlp
        self.ydl_opts = {
            'quiet': True,
            'no_warnings': True,
            'extract_flat': True,
        }
    
    def _configure_ssl(self):
        """Configure SSL to handle certificate issues on macOS."""
        try:
            ssl.create_default_context()
        except Exception:
            ssl._create_default_https_context = ssl._create_unverified_context
            print("⚠️  SSL certificate verification disabled (macOS compatibility)")
    
    def sanitize_filename(self, title: str) -> str:
        """Sanitize title for use as filename."""
        sanitized = re.sub(r'[<>:"/\\|?*]', '', title)
        sanitized = re.sub(r'\s+', '_', sanitized)
        sanitized = sanitized[:100]
        sanitized = sanitized.rstrip('. ')
        return sanitized if sanitized else "untitled_video"
    
    def get_available_transcript(self, video_id: str) -> Optional[Tuple[str, str, str]]:
        """
        Get the best available transcript for a video.
        
        Returns:
            Tuple of (transcript_text, language_code, language_name) or None
        """
        try:
            transcript_list = YouTubeTranscriptApi.list_transcripts(video_id)
            
            # Priority order: English variants first, then any other language
            preferred_codes = ['en', 'en-US', 'en-GB', 'en-CA', 'en-AU']
            
            # Try preferred languages first
            for lang_code in preferred_codes:
                try:
                    transcript = transcript_list.find_transcript([lang_code])
                    transcript_data = transcript.fetch()
                    text = "\n".join([
                        item.text if hasattr(item, 'text') else item['text'] 
                        for item in transcript_data
                    ])
                    return text, lang_code, transcript.language
                except NoTranscriptFound:
                    continue
            
            # If no English, try any available language
            for transcript in transcript_list:
                try:
                    transcript_data = transcript.fetch()
                    text = "\n".join([
                        item.text if hasattr(item, 'text') else item['text'] 
                        for item in transcript_data
                    ])
                    return text, transcript.language_code, transcript.language
                except Exception:
                    continue
            
            return None
            
        except (TranscriptsDisabled, Exception):
            return None
    
    def translate_text(self, text: str, source_lang: str, target_lang: str) -> Optional[str]:
        """Translate text from source to target language."""
        try:
            # Skip translation if already in target language
            if source_lang.startswith(target_lang):
                return text
            
            translator = GoogleTranslator(source=source_lang, target=target_lang)
            
            # Handle long texts by chunking
            max_chunk_size = 4500
            if len(text) <= max_chunk_size:
                return translator.translate(text)
            
            # Split into chunks
            chunks = []
            sentences = text.split('. ')
            current_chunk = ""
            
            for sentence in sentences:
                if len(current_chunk + sentence + '. ') <= max_chunk_size:
                    current_chunk += sentence + '. '
                else:
                    if current_chunk:
                        chunks.append(current_chunk.strip())
                    current_chunk = sentence + '. '
            
            if current_chunk:
                chunks.append(current_chunk.strip())
            
            # Translate chunks
            translated_chunks = []
            for chunk in chunks:
                try:
                    result = translator.translate(chunk)
                    translated_chunks.append(result)
                    time.sleep(0.1)  # Small delay
                except Exception:
                    translated_chunks.append(chunk)  # Fallback to original
            
            return " ".join(translated_chunks)
            
        except Exception as e:
            print(f"    ⚠️  Translation error: {e}")
            return None
    
    def process_single_video(self, video_id: str, video_title: str, output_dir: Path) -> bool:
        """Process a single video and save transcripts."""
        print(f"    🆔 Video ID: {video_id}")
        
        # Get any available transcript
        result = self.get_available_transcript(video_id)
        if not result:
            print(f"    ❌ No transcripts available")
            return False
        
        original_text, lang_code, lang_name = result
        print(f"    📝 Found transcript in: {lang_name} ({lang_code})")
        
        # Create language-specific directories
        english_dir = output_dir / "english"
        french_dir = output_dir / "french"
        english_dir.mkdir(exist_ok=True)
        french_dir.mkdir(exist_ok=True)
        
        success = False
        
        # Save/translate to English
        if lang_code.startswith('en'):
            # Already English
            english_file = english_dir / f"{video_title}_en.txt"
            with open(english_file, 'w', encoding='utf-8') as f:
                f.write(f"Video Title: {video_title}\n")
                f.write(f"Video ID: {video_id}\n")
                f.write(f"Language: English ({lang_code})\n")
                f.write("-" * 50 + "\n\n")
                f.write(original_text)
            print(f"    ✅ English transcript saved")
            success = True
            
            # Translate to French
            french_text = self.translate_text(original_text, 'en', 'fr')
            if french_text:
                french_file = french_dir / f"{video_title}_fr.txt"
                with open(french_file, 'w', encoding='utf-8') as f:
                    f.write(f"Video Title: {video_title}\n")
                    f.write(f"Video ID: {video_id}\n")
                    f.write(f"Language: French (translated from {lang_code})\n")
                    f.write("-" * 50 + "\n\n")
                    f.write(french_text)
                print(f"    ✅ French transcript saved (translated)")
        else:
            # Translate to English
            english_text = self.translate_text(original_text, lang_code, 'en')
            if english_text:
                english_file = english_dir / f"{video_title}_en.txt"
                with open(english_file, 'w', encoding='utf-8') as f:
                    f.write(f"Video Title: {video_title}\n")
                    f.write(f"Video ID: {video_id}\n")
                    f.write(f"Language: English (translated from {lang_name})\n")
                    f.write("-" * 50 + "\n\n")
                    f.write(english_text)
                print(f"    ✅ English transcript saved (translated from {lang_name})")
                success = True
                
                # Translate to French
                french_text = self.translate_text(english_text, 'en', 'fr')
                if french_text:
                    french_file = french_dir / f"{video_title}_fr.txt"
                    with open(french_file, 'w', encoding='utf-8') as f:
                        f.write(f"Video Title: {video_title}\n")
                        f.write(f"Video ID: {video_id}\n")
                        f.write(f"Language: French (translated from {lang_name} via English)\n")
                        f.write("-" * 50 + "\n\n")
                        f.write(french_text)
                    print(f"    ✅ French transcript saved (translated)")
        
        return success

def main():
    """Main function."""
    print("🌐 Flexible YouTube Playlist Transcript Downloader")
    print("📝 Handles any available language and translates to English/French")
    print("=" * 60)
    
    playlist_url = input("Please paste the YouTube playlist URL: ").strip()
    if not playlist_url:
        print("❌ No URL provided. Exiting.")
        return
    
    downloader = FlexibleTranscriptDownloader()
    
    try:
        # Get playlist info
        with yt_dlp.YoutubeDL(downloader.ydl_opts) as ydl:
            playlist_info = ydl.extract_info(playlist_url, download=False)
            
            if 'entries' not in playlist_info:
                print("❌ No videos found in playlist")
                return
            
            playlist_title = playlist_info.get('title', 'Unknown_Playlist')
            playlist_title = downloader.sanitize_filename(playlist_title)
            
            videos = []
            for entry in playlist_info['entries']:
                if entry and 'id' in entry and 'title' in entry:
                    videos.append((entry['id'], entry['title']))
        
        if not videos:
            print("❌ No videos found in playlist")
            return
        
        # Setup output directory
        output_dir = Path("transcripts") / playlist_title
        output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"📋 Playlist: {playlist_title}")
        print(f"📹 Found {len(videos)} videos")
        print(f"📁 Saving to: {output_dir.absolute()}")
        print()
        
        success_count = 0
        
        for i, (video_id, title) in enumerate(videos, 1):
            sanitized_title = downloader.sanitize_filename(title)
            print(f"[{i}/{len(videos)}] Processing: {title}")
            
            if downloader.process_single_video(video_id, sanitized_title, output_dir):
                success_count += 1
            
            # Small delay to avoid rate limiting
            if i < len(videos):
                time.sleep(2)
            print()
        
        print("=" * 60)
        print(f"📊 Successfully processed: {success_count}/{len(videos)} videos")
        print(f"📁 Files saved in: {output_dir.absolute()}")
        
    except Exception as e:
        error_msg = str(e)
        if "429" in error_msg or "Too Many Requests" in error_msg:
            print("❌ Rate limited by YouTube API")
            print("💡 Wait 30-60 minutes and try again")
        else:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()

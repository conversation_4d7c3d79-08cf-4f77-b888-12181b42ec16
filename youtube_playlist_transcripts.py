#!/usr/bin/env python3
"""
YouTube Playlist Transcript Downloader

This script downloads English and French transcripts for all videos in a YouTube playlist.
It handles SSL certificate errors gracefully and organizes transcripts into language-specific folders.

Requirements:
- yt-dlp
- youtube-transcript-api

Install with: pip install yt-dlp youtube-transcript-api
"""

import os
import re
import ssl
import sys
import urllib.request
from pathlib import Path
from typing import List, Optional, Tuple

try:
    import yt_dlp
    from youtube_transcript_api import YouTubeTranscriptApi, NoTranscriptFound, TranscriptsDisabled
    from googletrans import Translator
except ImportError as e:
    print(f"Error: Missing required library. {e}")
    print("Please install required packages with:")
    print("pip install yt-dlp youtube-transcript-api googletrans==4.0.0rc1")
    sys.exit(1)


class YouTubePlaylistTranscriptDownloader:
    """Downloads transcripts for all videos in a YouTube playlist."""

    def __init__(self, base_dir: str = "transcripts", enable_translation: bool = True):
        """
        Initialize the downloader.

        Args:
            base_dir: Base directory to save transcripts
            enable_translation: Whether to enable translation for missing languages
        """
        self.base_dir = Path(base_dir)
        self.english_dir = self.base_dir / "english"
        self.french_dir = self.base_dir / "french"
        self.enable_translation = enable_translation

        # Create directories
        self.english_dir.mkdir(parents=True, exist_ok=True)
        self.french_dir.mkdir(parents=True, exist_ok=True)

        # Initialize translator if translation is enabled
        self.translator = None
        if self.enable_translation:
            try:
                self.translator = Translator()
                print("🌐 Translation service initialized")
            except Exception as e:
                print(f"⚠️  Translation service unavailable: {e}")
                self.enable_translation = False
        
        # Language configurations
        self.languages = {
            'english': {
                'codes': ['en', 'en-US', 'en-GB', 'en-CA', 'en-AU'],
                'dir': self.english_dir,
                'suffix': 'en'
            },
            'french': {
                'codes': ['fr', 'fr-FR', 'fr-CA'],
                'dir': self.french_dir,
                'suffix': 'fr'
            }
        }
        
        # Configure SSL for macOS
        self._configure_ssl()
        
        # Configure yt-dlp
        self.ydl_opts = {
            'quiet': True,
            'no_warnings': True,
            'extract_flat': True,
        }
    
    def _configure_ssl(self):
        """Configure SSL to handle certificate issues on macOS."""
        try:
            # Try to create default context first
            ssl.create_default_context()
        except Exception:
            # If that fails, use unverified context (for macOS certificate issues)
            ssl._create_default_https_context = ssl._create_unverified_context
            print("⚠️  SSL certificate verification disabled (macOS compatibility)")
    
    def sanitize_filename(self, title: str) -> str:
        """
        Sanitize video title for use as filename.
        
        Args:
            title: Video title
            
        Returns:
            Sanitized filename
        """
        # Remove invalid characters
        sanitized = re.sub(r'[<>:"/\\|?*]', '', title)
        # Replace spaces with underscores
        sanitized = re.sub(r'\s+', '_', sanitized)
        # Limit length
        sanitized = sanitized[:100]
        # Remove trailing dots and spaces
        sanitized = sanitized.rstrip('. ')
        
        return sanitized if sanitized else "untitled_video"

    def _extract_transcript_text(self, transcript_data) -> str:
        """
        Extract text from transcript data, handling different API formats.

        Args:
            transcript_data: Transcript data from YouTube API

        Returns:
            Extracted transcript text
        """
        text_parts = []

        # Handle FetchedTranscript object (newer API)
        if hasattr(transcript_data, '__iter__'):
            for item in transcript_data:
                try:
                    # Try different ways to access the text
                    if hasattr(item, 'text'):
                        # New format: FetchedTranscriptSnippet with text attribute
                        text_parts.append(item.text)
                    elif isinstance(item, dict) and 'text' in item:
                        # Old format: dictionary with 'text' key
                        text_parts.append(item['text'])
                    elif hasattr(item, 'get'):
                        # Fallback: try get method
                        text_parts.append(item.get('text', ''))
                    else:
                        # Last resort: convert to string
                        text_parts.append(str(item))
                except Exception as e:
                    print(f"    🔍 Warning: Could not extract text from item: {e}")
                    continue
        else:
            # If transcript_data is not iterable, try to extract text directly
            if hasattr(transcript_data, 'text'):
                text_parts.append(transcript_data.text)
            else:
                text_parts.append(str(transcript_data))

        return "\n".join(text_parts)

    def translate_text(self, text: str, target_language: str = 'fr') -> Optional[str]:
        """
        Translate text to target language.

        Args:
            text: Text to translate
            target_language: Target language code (default: 'fr' for French)

        Returns:
            Translated text or None if translation fails
        """
        if not self.enable_translation or not self.translator:
            return None

        try:
            # Split text into chunks to handle long transcripts
            max_chunk_size = 4500  # Google Translate has a 5000 character limit
            chunks = []

            if len(text) <= max_chunk_size:
                chunks = [text]
            else:
                # Split by sentences to maintain context
                sentences = text.split('. ')
                current_chunk = ""

                for sentence in sentences:
                    if len(current_chunk + sentence + '. ') <= max_chunk_size:
                        current_chunk += sentence + '. '
                    else:
                        if current_chunk:
                            chunks.append(current_chunk.strip())
                        current_chunk = sentence + '. '

                if current_chunk:
                    chunks.append(current_chunk.strip())

            # Translate each chunk
            translated_chunks = []
            for i, chunk in enumerate(chunks):
                try:
                    result = self.translator.translate(chunk, dest=target_language)
                    translated_chunks.append(result.text)

                    # Add small delay to avoid rate limiting
                    if i < len(chunks) - 1:
                        import time
                        time.sleep(0.1)

                except Exception as e:
                    print(f"    ⚠️  Translation failed for chunk {i+1}: {e}")
                    translated_chunks.append(chunk)  # Use original text as fallback

            return " ".join(translated_chunks)

        except Exception as e:
            print(f"    ❌ Translation error: {e}")
            return None

    def _save_translated_transcript(self, english_text: str, video_title: str,
                                  language_config: dict, language_name: str) -> bool:
        """
        Translate and save English transcript to target language.

        Args:
            english_text: English transcript text
            video_title: Video title (sanitized)
            language_config: Language configuration dict
            language_name: Language name for display

        Returns:
            True if translation and save was successful
        """
        try:
            # Translate the text
            target_lang = 'fr' if language_name.lower() == 'french' else 'fr'
            translated_text = self.translate_text(english_text, target_lang)

            if not translated_text:
                print(f"    ❌ Translation to {language_name} failed")
                return False

            # Save translated transcript
            filename = f"{video_title}_{language_config['suffix']}.txt"
            file_path = language_config['dir'] / filename

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(f"Video Title: {video_title}\n")
                f.write(f"Video ID: [Translated from English]\n")
                f.write(f"Language: {language_name} (translated)\n")
                f.write("-" * 50 + "\n\n")
                f.write(translated_text)

            print(f"    ✅ {language_name} transcript saved (translated)")
            return True

        except Exception as e:
            print(f"    ❌ Error saving translated {language_name} transcript: {e}")
            return False

    def _get_english_transcript_text(self, video_id: str) -> Optional[str]:
        """
        Get English transcript text for translation purposes.

        Args:
            video_id: YouTube video ID

        Returns:
            English transcript text or None if not available
        """
        try:
            transcript_list = YouTubeTranscriptApi.list_transcripts(video_id)
            english_codes = ['en', 'en-US', 'en-GB', 'en-CA', 'en-AU']

            for lang_code in english_codes:
                try:
                    transcript = transcript_list.find_transcript([lang_code])
                    transcript_data = transcript.fetch()
                    return self._extract_transcript_text(transcript_data)
                except NoTranscriptFound:
                    continue

            return None

        except Exception:
            return None
    
    def get_playlist_videos(self, playlist_url: str) -> List[Tuple[str, str]]:
        """
        Extract video information from playlist.
        
        Args:
            playlist_url: YouTube playlist URL
            
        Returns:
            List of (video_id, title) tuples
        """
        try:
            with yt_dlp.YoutubeDL(self.ydl_opts) as ydl:
                playlist_info = ydl.extract_info(playlist_url, download=False)
                
                if 'entries' not in playlist_info:
                    raise ValueError("No videos found in playlist")
                
                videos = []
                for entry in playlist_info['entries']:
                    if entry and 'id' in entry and 'title' in entry:
                        videos.append((entry['id'], entry['title']))
                
                return videos
                
        except Exception as e:
            raise Exception(f"Failed to extract playlist information: {e}")
    
    def download_transcript(self, video_id: str, video_title: str,
                          language_name: str, language_config: dict,
                          english_transcript_text: Optional[str] = None) -> bool:
        """
        Download transcript for a specific language.

        Args:
            video_id: YouTube video ID
            video_title: Video title (sanitized)
            language_name: Language name for display
            language_config: Language configuration dict
            english_transcript_text: English transcript text for translation (optional)

        Returns:
            True if transcript was downloaded successfully
        """
        try:
            # Get available transcripts
            transcript_list = YouTubeTranscriptApi.list_transcripts(video_id)
            
            # Try each language code
            transcript = None
            used_code = None
            
            for lang_code in language_config['codes']:
                try:
                    transcript = transcript_list.find_transcript([lang_code])
                    used_code = lang_code
                    break
                except NoTranscriptFound:
                    continue
            
            if not transcript:
                # Try translation if English transcript is available and this is French
                if (language_name.lower() == 'french' and english_transcript_text and
                    self.enable_translation):
                    print(f"    🌐 No native {language_name} transcript, attempting translation...")
                    return self._save_translated_transcript(
                        english_transcript_text, video_title, language_config, language_name
                    )
                else:
                    print(f"    ❌ No {language_name} transcript available")
                    return False
            
            # Fetch transcript text
            transcript_data = transcript.fetch()
            # Handle different transcript API formats
            transcript_text = self._extract_transcript_text(transcript_data)
            
            # Save to file
            filename = f"{video_title}_{language_config['suffix']}.txt"
            file_path = language_config['dir'] / filename
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(f"Video Title: {video_title}\n")
                f.write(f"Video ID: {video_id}\n")
                f.write(f"Language: {language_name} ({used_code})\n")
                f.write("-" * 50 + "\n\n")
                f.write(transcript_text)
            
            print(f"    ✅ {language_name} transcript saved ({used_code})")
            return True
            
        except TranscriptsDisabled:
            print(f"    ❌ Transcripts disabled for this video ({language_name})")
            return False
        except Exception as e:
            print(f"    ❌ Error downloading {language_name} transcript: {e}")
            # Add debug information for troubleshooting
            if "subscriptable" in str(e):
                print(f"    🔍 Debug: This appears to be a transcript format issue. Trying alternative approach...")
                try:
                    # Alternative approach for newer API versions
                    transcript_data = transcript.fetch()
                    if transcript_data and len(transcript_data) > 0:
                        first_item = transcript_data[0]
                        print(f"    🔍 Debug: First item type: {type(first_item)}")
                        print(f"    🔍 Debug: First item attributes: {dir(first_item)}")
                except Exception as debug_e:
                    print(f"    🔍 Debug failed: {debug_e}")
            return False
    
    def download_playlist_transcripts(self, playlist_url: str):
        """
        Download transcripts for all videos in a playlist.
        
        Args:
            playlist_url: YouTube playlist URL
        """
        print("🎬 YouTube Playlist Transcript Downloader")
        print("=" * 50)
        
        # Validate URL
        if not self.is_valid_playlist_url(playlist_url):
            raise ValueError("Invalid YouTube playlist URL")
        
        print(f"📋 Extracting playlist information...")
        
        try:
            videos = self.get_playlist_videos(playlist_url)
        except Exception as e:
            raise Exception(f"Failed to get playlist videos: {e}")
        
        if not videos:
            print("❌ No videos found in playlist")
            return
        
        print(f"📹 Found {len(videos)} videos in playlist")
        print(f"📁 Saving transcripts to: {self.base_dir.absolute()}")
        print()
        
        # Download transcripts
        success_count = {'english': 0, 'french': 0}
        
        for i, (video_id, title) in enumerate(videos, 1):
            sanitized_title = self.sanitize_filename(title)
            print(f"[{i}/{len(videos)}] Processing: {title}")
            print(f"    🆔 Video ID: {video_id}")

            # Download English transcript first
            english_transcript_text = None
            english_config = self.languages['english']
            if self.download_transcript(video_id, sanitized_title, 'English', english_config):
                success_count['english'] += 1

                # Get the English transcript text for translation
                if self.enable_translation:
                    english_transcript_text = self._get_english_transcript_text(video_id)

            # Download French transcript (with translation fallback)
            french_config = self.languages['french']
            if self.download_transcript(video_id, sanitized_title, 'French', french_config, english_transcript_text):
                success_count['french'] += 1

            print()
        
        # Summary
        print("=" * 50)
        print("📊 Download Summary:")
        print(f"   English transcripts: {success_count['english']}/{len(videos)}")
        print(f"   French transcripts:  {success_count['french']}/{len(videos)}")
        print(f"   📁 Files saved in: {self.base_dir.absolute()}")
    
    @staticmethod
    def is_valid_playlist_url(url: str) -> bool:
        """
        Validate YouTube playlist URL.
        
        Args:
            url: URL to validate
            
        Returns:
            True if valid playlist URL
        """
        playlist_patterns = [
            r'youtube\.com/playlist\?list=',
            r'youtube\.com/watch\?.*list=',
            r'youtu\.be/.*list='
        ]
        
        return any(re.search(pattern, url) for pattern in playlist_patterns)


def main():
    """Main function to run the script."""
    print("This script requires 'yt-dlp', 'youtube-transcript-api', and 'googletrans'")
    print("Install with: pip install yt-dlp youtube-transcript-api googletrans==4.0.0rc1")
    print("📝 Note: French transcripts will be auto-translated from English if not available natively")
    print()
    
    # Get playlist URL from user
    playlist_url = input("Please paste the YouTube playlist URL: ").strip()
    
    if not playlist_url:
        print("❌ No URL provided. Exiting.")
        return
    
    try:
        downloader = YouTubePlaylistTranscriptDownloader()
        downloader.download_playlist_transcripts(playlist_url)
        
    except KeyboardInterrupt:
        print("\n⚠️  Download interrupted by user")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

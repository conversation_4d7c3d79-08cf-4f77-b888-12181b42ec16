#!/usr/bin/env python3
"""
YouTube Playlist Transcript Downloader

This script downloads English and French transcripts for all videos in a YouTube playlist.
It handles SSL certificate errors gracefully and organizes transcripts into language-specific folders.

Requirements:
- yt-dlp
- youtube-transcript-api

Install with: pip install yt-dlp youtube-transcript-api
"""

import os
import re
import ssl
import sys
import urllib.request
from pathlib import Path
from typing import List, Optional, Tuple

try:
    import yt_dlp
    from youtube_transcript_api import YouTubeTranscriptApi, NoTranscriptFound, TranscriptsDisabled
except ImportError as e:
    print(f"Error: Missing required library. {e}")
    print("Please install required packages with:")
    print("pip install yt-dlp youtube-transcript-api")
    sys.exit(1)


class YouTubePlaylistTranscriptDownloader:
    """Downloads transcripts for all videos in a YouTube playlist."""
    
    def __init__(self, base_dir: str = "transcripts"):
        """
        Initialize the downloader.
        
        Args:
            base_dir: Base directory to save transcripts
        """
        self.base_dir = Path(base_dir)
        self.english_dir = self.base_dir / "english"
        self.french_dir = self.base_dir / "french"
        
        # Create directories
        self.english_dir.mkdir(parents=True, exist_ok=True)
        self.french_dir.mkdir(parents=True, exist_ok=True)
        
        # Language configurations
        self.languages = {
            'english': {
                'codes': ['en', 'en-US', 'en-GB', 'en-CA', 'en-AU'],
                'dir': self.english_dir,
                'suffix': 'en'
            },
            'french': {
                'codes': ['fr', 'fr-FR', 'fr-CA'],
                'dir': self.french_dir,
                'suffix': 'fr'
            }
        }
        
        # Configure SSL for macOS
        self._configure_ssl()
        
        # Configure yt-dlp
        self.ydl_opts = {
            'quiet': True,
            'no_warnings': True,
            'extract_flat': True,
        }
    
    def _configure_ssl(self):
        """Configure SSL to handle certificate issues on macOS."""
        try:
            # Try to create default context first
            ssl.create_default_context()
        except Exception:
            # If that fails, use unverified context (for macOS certificate issues)
            ssl._create_default_https_context = ssl._create_unverified_context
            print("⚠️  SSL certificate verification disabled (macOS compatibility)")
    
    def sanitize_filename(self, title: str) -> str:
        """
        Sanitize video title for use as filename.
        
        Args:
            title: Video title
            
        Returns:
            Sanitized filename
        """
        # Remove invalid characters
        sanitized = re.sub(r'[<>:"/\\|?*]', '', title)
        # Replace spaces with underscores
        sanitized = re.sub(r'\s+', '_', sanitized)
        # Limit length
        sanitized = sanitized[:100]
        # Remove trailing dots and spaces
        sanitized = sanitized.rstrip('. ')
        
        return sanitized if sanitized else "untitled_video"

    def _extract_transcript_text(self, transcript_data) -> str:
        """
        Extract text from transcript data, handling different API formats.

        Args:
            transcript_data: Transcript data from YouTube API

        Returns:
            Extracted transcript text
        """
        text_parts = []

        # Handle FetchedTranscript object (newer API)
        if hasattr(transcript_data, '__iter__'):
            for item in transcript_data:
                try:
                    # Try different ways to access the text
                    if hasattr(item, 'text'):
                        # New format: FetchedTranscriptSnippet with text attribute
                        text_parts.append(item.text)
                    elif isinstance(item, dict) and 'text' in item:
                        # Old format: dictionary with 'text' key
                        text_parts.append(item['text'])
                    elif hasattr(item, 'get'):
                        # Fallback: try get method
                        text_parts.append(item.get('text', ''))
                    else:
                        # Last resort: convert to string
                        text_parts.append(str(item))
                except Exception as e:
                    print(f"    🔍 Warning: Could not extract text from item: {e}")
                    continue
        else:
            # If transcript_data is not iterable, try to extract text directly
            if hasattr(transcript_data, 'text'):
                text_parts.append(transcript_data.text)
            else:
                text_parts.append(str(transcript_data))

        return "\n".join(text_parts)
    
    def get_playlist_videos(self, playlist_url: str) -> List[Tuple[str, str]]:
        """
        Extract video information from playlist.
        
        Args:
            playlist_url: YouTube playlist URL
            
        Returns:
            List of (video_id, title) tuples
        """
        try:
            with yt_dlp.YoutubeDL(self.ydl_opts) as ydl:
                playlist_info = ydl.extract_info(playlist_url, download=False)
                
                if 'entries' not in playlist_info:
                    raise ValueError("No videos found in playlist")
                
                videos = []
                for entry in playlist_info['entries']:
                    if entry and 'id' in entry and 'title' in entry:
                        videos.append((entry['id'], entry['title']))
                
                return videos
                
        except Exception as e:
            raise Exception(f"Failed to extract playlist information: {e}")
    
    def download_transcript(self, video_id: str, video_title: str, 
                          language_name: str, language_config: dict) -> bool:
        """
        Download transcript for a specific language.
        
        Args:
            video_id: YouTube video ID
            video_title: Video title (sanitized)
            language_name: Language name for display
            language_config: Language configuration dict
            
        Returns:
            True if transcript was downloaded successfully
        """
        try:
            # Get available transcripts
            transcript_list = YouTubeTranscriptApi.list_transcripts(video_id)
            
            # Try each language code
            transcript = None
            used_code = None
            
            for lang_code in language_config['codes']:
                try:
                    transcript = transcript_list.find_transcript([lang_code])
                    used_code = lang_code
                    break
                except NoTranscriptFound:
                    continue
            
            if not transcript:
                print(f"    ❌ No {language_name} transcript available")
                return False
            
            # Fetch transcript text
            transcript_data = transcript.fetch()
            # Handle different transcript API formats
            transcript_text = self._extract_transcript_text(transcript_data)
            
            # Save to file
            filename = f"{video_title}_{language_config['suffix']}.txt"
            file_path = language_config['dir'] / filename
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(f"Video Title: {video_title}\n")
                f.write(f"Video ID: {video_id}\n")
                f.write(f"Language: {language_name} ({used_code})\n")
                f.write("-" * 50 + "\n\n")
                f.write(transcript_text)
            
            print(f"    ✅ {language_name} transcript saved ({used_code})")
            return True
            
        except TranscriptsDisabled:
            print(f"    ❌ Transcripts disabled for this video ({language_name})")
            return False
        except Exception as e:
            print(f"    ❌ Error downloading {language_name} transcript: {e}")
            # Add debug information for troubleshooting
            if "subscriptable" in str(e):
                print(f"    🔍 Debug: This appears to be a transcript format issue. Trying alternative approach...")
                try:
                    # Alternative approach for newer API versions
                    transcript_data = transcript.fetch()
                    if transcript_data and len(transcript_data) > 0:
                        first_item = transcript_data[0]
                        print(f"    🔍 Debug: First item type: {type(first_item)}")
                        print(f"    🔍 Debug: First item attributes: {dir(first_item)}")
                except Exception as debug_e:
                    print(f"    🔍 Debug failed: {debug_e}")
            return False
    
    def download_playlist_transcripts(self, playlist_url: str):
        """
        Download transcripts for all videos in a playlist.
        
        Args:
            playlist_url: YouTube playlist URL
        """
        print("🎬 YouTube Playlist Transcript Downloader")
        print("=" * 50)
        
        # Validate URL
        if not self.is_valid_playlist_url(playlist_url):
            raise ValueError("Invalid YouTube playlist URL")
        
        print(f"📋 Extracting playlist information...")
        
        try:
            videos = self.get_playlist_videos(playlist_url)
        except Exception as e:
            raise Exception(f"Failed to get playlist videos: {e}")
        
        if not videos:
            print("❌ No videos found in playlist")
            return
        
        print(f"📹 Found {len(videos)} videos in playlist")
        print(f"📁 Saving transcripts to: {self.base_dir.absolute()}")
        print()
        
        # Download transcripts
        success_count = {'english': 0, 'french': 0}
        
        for i, (video_id, title) in enumerate(videos, 1):
            sanitized_title = self.sanitize_filename(title)
            print(f"[{i}/{len(videos)}] Processing: {title}")
            print(f"    🆔 Video ID: {video_id}")
            
            # Download for each language
            for lang_name, lang_config in self.languages.items():
                if self.download_transcript(video_id, sanitized_title, lang_name.title(), lang_config):
                    success_count[lang_name] += 1
            
            print()
        
        # Summary
        print("=" * 50)
        print("📊 Download Summary:")
        print(f"   English transcripts: {success_count['english']}/{len(videos)}")
        print(f"   French transcripts:  {success_count['french']}/{len(videos)}")
        print(f"   📁 Files saved in: {self.base_dir.absolute()}")
    
    @staticmethod
    def is_valid_playlist_url(url: str) -> bool:
        """
        Validate YouTube playlist URL.
        
        Args:
            url: URL to validate
            
        Returns:
            True if valid playlist URL
        """
        playlist_patterns = [
            r'youtube\.com/playlist\?list=',
            r'youtube\.com/watch\?.*list=',
            r'youtu\.be/.*list='
        ]
        
        return any(re.search(pattern, url) for pattern in playlist_patterns)


def main():
    """Main function to run the script."""
    print("This script requires 'yt-dlp' and 'youtube-transcript-api'")
    print("Install with: pip install yt-dlp youtube-transcript-api")
    print()
    
    # Get playlist URL from user
    playlist_url = input("Please paste the YouTube playlist URL: ").strip()
    
    if not playlist_url:
        print("❌ No URL provided. Exiting.")
        return
    
    try:
        downloader = YouTubePlaylistTranscriptDownloader()
        downloader.download_playlist_transcripts(playlist_url)
        
    except KeyboardInterrupt:
        print("\n⚠️  Download interrupted by user")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

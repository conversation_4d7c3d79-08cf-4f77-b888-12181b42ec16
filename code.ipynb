import ssl
# --- FIX for SSL Certificate Error ---
ssl._create_default_https_context = ssl._create_unverified_context

import os
import re
from pytube import Playlist
from youtube_transcript_api import YouTubeTranscript<PERSON>pi, NoTranscriptFound, TranscriptsDisabled

def sanitize_filename(title):
    """Removes characters that are not allowed in filenames."""
    sanitized_title = re.sub(r'[\\/*?:"<>|]', "", title)
    return sanitized_title.replace(" ", "_")

def download_transcript(video_id, video_title, lang_code, lang_name, subfolder):
    try:
        transcript_list = YouTubeTranscriptApi.list_transcripts(video_id)
        transcript = transcript_list.find_transcript(lang_code)
        transcript_text = " ".join([item['text'] for item in transcript.fetch()])

        file_path = os.path.join(subfolder, f"{video_title}_{lang_code[0]}.txt")
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(transcript_text)
        print(f"  -> {lang_name} transcript saved.")
    except (NoTranscriptFound, TranscriptsDisabled):
        print(f"  -> No {lang_name} transcript found for this video.")
    except Exception as e:
        print(f"  -> An error occurred while fetching the {lang_name} transcript: {e}")

def get_playlist_transcripts(playlist_url):
    try:
        playlist = Playlist(playlist_url)
        playlist._video_regex = re.compile(r"\"url\":\"(/watch\?v=[\w-]*)")

        if not playlist.video_urls:
            print("Could not find any videos in the playlist. Make sure the playlist is public.")
            return

        print(f"Found {len(playlist.video_urls)} videos in the playlist.")

    except Exception as e:
        print(f"Error: Could not fetch playlist. Please check the URL. Details: {e}")
        return

    base_dir = "transcripts"
    english_dir = os.path.join(base_dir, "english")
    french_dir = os.path.join(base_dir, "french")
    os.makedirs(english_dir, exist_ok=True)
    os.makedirs(french_dir, exist_ok=True)

    print("\nStarting transcript download process...")
    print("-" * 40)

    for i, video in enumerate(playlist.videos):
        sanitized_title = sanitize_filename(video.title)
        print(f"\n({i+1}/{len(playlist.videos)}) Processing: {video.title}")
        print(f"  URL: {video.watch_url}")

        download_transcript(video.video_id, sanitized_title, ['en', 'en-US', 'en-GB'], "English", english_dir)
        download_transcript(video.video_id, sanitized_title, ['fr', 'fr-FR'], "French", french_dir)

if __name__ == "__main__":
    print("This script requires 'pytube' and 'youtube-transcript-api'.")
    print("Install with: pip install pytube youtube-transcript-api\n")

    playlist_url_input = input("Please paste the YouTube playlist URL and press Enter: ")

    if playlist_url_input:
        get_playlist_transcripts(playlist_url_input)
        print("\n" + "="*40)
        print("Transcript download process finished.")
        print(f"Files are saved in the '{os.path.join('transcripts', 'english')}' and '{os.path.join('transcripts', 'french')}' folders.")
    else:
        print("No URL provided. Exiting.")



#!/usr/bin/env python3
"""
Anti-Rate-Limit YouTube Transcript Downloader

Uses multiple techniques to avoid rate limiting:
- User agent rotation
- Request delays
- Session management
- Fallback methods
"""

import os
import re
import ssl
import sys
import time
import random
from pathlib import Path
from typing import List, Optional, Tu<PERSON>

try:
    import yt_dlp
    from youtube_transcript_api import YouTubeTranscriptApi, NoTranscriptFound, TranscriptsDisabled
    from deep_translator import GoogleTranslator
    import requests
except ImportError as e:
    print(f"Error: Missing required library. {e}")
    print("Please install required packages with:")
    print("pip install yt-dlp youtube-transcript-api deep-translator requests")
    sys.exit(1)

class AntiRateLimitDownloader:
    """Downloads transcripts with anti-rate-limiting techniques."""
    
    def __init__(self):
        # Configure SSL for macOS
        self._configure_ssl()
        
        # User agents to rotate
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0',
        ]
        
        # Configure yt-dlp
        self.ydl_opts = {
            'quiet': True,
            'no_warnings': True,
            'extract_flat': True,
        }
        
        # Session for requests
        self.session = requests.Session()
        self.current_user_agent = random.choice(self.user_agents)
        self.session.headers.update({'User-Agent': self.current_user_agent})
    
    def _configure_ssl(self):
        """Configure SSL to handle certificate issues on macOS."""
        try:
            ssl.create_default_context()
        except Exception:
            ssl._create_default_https_context = ssl._create_unverified_context
            print("⚠️  SSL certificate verification disabled (macOS compatibility)")
    
    def rotate_user_agent(self):
        """Rotate to a different user agent."""
        self.current_user_agent = random.choice(self.user_agents)
        self.session.headers.update({'User-Agent': self.current_user_agent})
        print(f"    🔄 Rotated user agent")
    
    def sanitize_filename(self, title: str) -> str:
        """Sanitize title for use as filename."""
        sanitized = re.sub(r'[<>:"/\\|?*]', '', title)
        sanitized = re.sub(r'\s+', '_', sanitized)
        sanitized = sanitized[:100]
        sanitized = sanitized.rstrip('. ')
        return sanitized if sanitized else "untitled_video"
    
    def get_transcript_with_retry(self, video_id: str, max_retries: int = 5) -> Optional[Tuple[str, str, str]]:
        """
        Get transcript with multiple retry strategies.
        
        Returns:
            Tuple of (transcript_text, language_code, language_name) or None
        """
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    # Rotate user agent on retry
                    self.rotate_user_agent()
                    
                    # Progressive delay
                    delay = min(2 ** attempt + random.uniform(0, 1), 10)
                    print(f"    ⏳ Waiting {delay:.1f} seconds before retry {attempt + 1}...")
                    time.sleep(delay)
                
                # Try to get transcript
                transcript_list = YouTubeTranscriptApi.list_transcripts(video_id)
                
                # Priority order: English variants first, then any other language
                preferred_codes = ['en', 'en-US', 'en-GB', 'en-CA', 'en-AU']
                
                # Try preferred languages first
                for lang_code in preferred_codes:
                    try:
                        transcript = transcript_list.find_transcript([lang_code])
                        transcript_data = transcript.fetch()
                        text = "\n".join([
                            item.text if hasattr(item, 'text') else item['text'] 
                            for item in transcript_data
                        ])
                        return text, lang_code, transcript.language
                    except NoTranscriptFound:
                        continue
                
                # If no English, try any available language
                for transcript in transcript_list:
                    try:
                        transcript_data = transcript.fetch()
                        text = "\n".join([
                            item.text if hasattr(item, 'text') else item['text'] 
                            for item in transcript_data
                        ])
                        return text, transcript.language_code, transcript.language
                    except Exception:
                        continue
                
                return None
                
            except Exception as e:
                error_msg = str(e)
                
                if "429" in error_msg or "Too Many Requests" in error_msg:
                    if attempt < max_retries - 1:
                        print(f"    ⚠️  Rate limited, retrying with different approach...")
                        continue
                    else:
                        print(f"    ❌ Rate limited after {max_retries} attempts")
                        return None
                elif "TranscriptsDisabled" in error_msg:
                    print(f"    ❌ Transcripts disabled for this video")
                    return None
                else:
                    if attempt < max_retries - 1:
                        print(f"    ⚠️  Error: {e}, retrying...")
                        continue
                    else:
                        print(f"    ❌ Failed after {max_retries} attempts: {e}")
                        return None
        
        return None
    
    def translate_text(self, text: str, source_lang: str, target_lang: str) -> Optional[str]:
        """Translate text from source to target language."""
        try:
            # Skip translation if already in target language
            if source_lang.startswith(target_lang):
                return text
            
            translator = GoogleTranslator(source=source_lang, target=target_lang)
            
            # Handle long texts by chunking
            max_chunk_size = 4500
            if len(text) <= max_chunk_size:
                return translator.translate(text)
            
            # Split into chunks
            chunks = []
            sentences = text.split('. ')
            current_chunk = ""
            
            for sentence in sentences:
                if len(current_chunk + sentence + '. ') <= max_chunk_size:
                    current_chunk += sentence + '. '
                else:
                    if current_chunk:
                        chunks.append(current_chunk.strip())
                    current_chunk = sentence + '. '
            
            if current_chunk:
                chunks.append(current_chunk.strip())
            
            # Translate chunks with delays
            translated_chunks = []
            for i, chunk in enumerate(chunks):
                try:
                    result = translator.translate(chunk)
                    translated_chunks.append(result)
                    
                    # Random delay between chunks
                    if i < len(chunks) - 1:
                        time.sleep(random.uniform(0.1, 0.3))
                        
                except Exception:
                    translated_chunks.append(chunk)  # Fallback to original
            
            return " ".join(translated_chunks)
            
        except Exception as e:
            print(f"    ⚠️  Translation error: {e}")
            return None
    
    def process_video(self, video_id: str, video_title: str, output_dir: Path) -> bool:
        """Process a single video and save transcripts."""
        print(f"    🆔 Video ID: {video_id}")
        
        # Get transcript with anti-rate-limiting
        result = self.get_transcript_with_retry(video_id)
        if not result:
            return False
        
        original_text, lang_code, lang_name = result
        print(f"    📝 Found transcript in: {lang_name} ({lang_code})")
        
        # Create language-specific directories
        english_dir = output_dir / "english"
        french_dir = output_dir / "french"
        english_dir.mkdir(exist_ok=True)
        french_dir.mkdir(exist_ok=True)
        
        success = False
        
        # Handle English
        if lang_code.startswith('en'):
            # Save original English
            english_file = english_dir / f"{video_title}_en.txt"
            with open(english_file, 'w', encoding='utf-8') as f:
                f.write(f"Video Title: {video_title}\n")
                f.write(f"Video ID: {video_id}\n")
                f.write(f"Language: English ({lang_code})\n")
                f.write("-" * 50 + "\n\n")
                f.write(original_text)
            print(f"    ✅ English transcript saved")
            success = True
            
            # Translate to French
            french_text = self.translate_text(original_text, 'en', 'fr')
            if french_text:
                french_file = french_dir / f"{video_title}_fr.txt"
                with open(french_file, 'w', encoding='utf-8') as f:
                    f.write(f"Video Title: {video_title}\n")
                    f.write(f"Video ID: {video_id}\n")
                    f.write(f"Language: French (translated from {lang_code})\n")
                    f.write("-" * 50 + "\n\n")
                    f.write(french_text)
                print(f"    ✅ French transcript saved (translated)")
        else:
            # Translate to English first
            english_text = self.translate_text(original_text, lang_code, 'en')
            if english_text:
                english_file = english_dir / f"{video_title}_en.txt"
                with open(english_file, 'w', encoding='utf-8') as f:
                    f.write(f"Video Title: {video_title}\n")
                    f.write(f"Video ID: {video_id}\n")
                    f.write(f"Language: English (translated from {lang_name})\n")
                    f.write("-" * 50 + "\n\n")
                    f.write(english_text)
                print(f"    ✅ English transcript saved (translated from {lang_name})")
                success = True
                
                # Translate to French
                french_text = self.translate_text(english_text, 'en', 'fr')
                if french_text:
                    french_file = french_dir / f"{video_title}_fr.txt"
                    with open(french_file, 'w', encoding='utf-8') as f:
                        f.write(f"Video Title: {video_title}\n")
                        f.write(f"Video ID: {video_id}\n")
                        f.write(f"Language: French (translated from {lang_name} via English)\n")
                        f.write("-" * 50 + "\n\n")
                        f.write(french_text)
                    print(f"    ✅ French transcript saved (translated)")
        
        return success

def main():
    """Main function."""
    print("🛡️  Anti-Rate-Limit YouTube Transcript Downloader")
    print("🔄 Uses user agent rotation and smart retry strategies")
    print("=" * 60)
    
    playlist_url = input("Please paste the YouTube playlist URL: ").strip()
    if not playlist_url:
        print("❌ No URL provided. Exiting.")
        return
    
    downloader = AntiRateLimitDownloader()
    
    try:
        # Get playlist info
        with yt_dlp.YoutubeDL(downloader.ydl_opts) as ydl:
            playlist_info = ydl.extract_info(playlist_url, download=False)
            
            if 'entries' not in playlist_info:
                print("❌ No videos found in playlist")
                return
            
            playlist_title = playlist_info.get('title', 'Unknown_Playlist')
            playlist_title = downloader.sanitize_filename(playlist_title)
            
            videos = []
            for entry in playlist_info['entries']:
                if entry and 'id' in entry and 'title' in entry:
                    videos.append((entry['id'], entry['title']))
        
        if not videos:
            print("❌ No videos found in playlist")
            return
        
        # Setup output directory
        output_dir = Path("transcripts") / playlist_title
        output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"📋 Playlist: {playlist_title}")
        print(f"📹 Found {len(videos)} videos")
        print(f"📁 Saving to: {output_dir.absolute()}")
        print()
        
        success_count = 0
        
        for i, (video_id, title) in enumerate(videos, 1):
            sanitized_title = downloader.sanitize_filename(title)
            print(f"[{i}/{len(videos)}] Processing: {title}")
            
            if downloader.process_video(video_id, sanitized_title, output_dir):
                success_count += 1
            
            # Random delay between videos
            if i < len(videos):
                delay = random.uniform(1, 3)
                time.sleep(delay)
            
            print()
        
        print("=" * 60)
        print(f"📊 Successfully processed: {success_count}/{len(videos)} videos")
        print(f"📁 Files saved in: {output_dir.absolute()}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()

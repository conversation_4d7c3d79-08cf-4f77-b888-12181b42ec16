#!/bin/bash
# Setup script for YouTube Playlist Transcript Downloader

echo "🚀 Setting up YouTube Playlist Transcript Downloader..."
echo "=================================================="

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.7+ first."
    exit 1
fi

echo "✅ Python 3 found: $(python3 --version)"

# Install dependencies
echo "📦 Installing dependencies..."
pip3 install -r requirements.txt

if [ $? -eq 0 ]; then
    echo "✅ Dependencies installed successfully!"
    echo ""
    echo "🎬 Ready to use! Run the script with:"
    echo "   python3 youtube_playlist_transcripts.py"
    echo ""
    echo "📝 Features:"
    echo "   • Downloads English transcripts"
    echo "   • Auto-translates to French when needed"
    echo "   • Organizes files in language-specific folders"
else
    echo "❌ Failed to install dependencies. Please check your internet connection and try again."
    exit 1
fi

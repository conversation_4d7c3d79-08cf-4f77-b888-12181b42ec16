# 🔑 YouTube API Setup Guide (5 Minutes)

## Why Use YouTube API?

- ✅ **10,000+ requests per day** (vs ~100 with current method)
- ✅ **No IP rate limiting** - uses API keys instead
- ✅ **Official Google support** - more reliable
- ✅ **Better error handling** - clear quota management
- ✅ **Faster processing** - optimized for bulk operations

## Quick Setup Steps

### Step 1: Go to Google Cloud Console
🔗 **Link**: https://console.developers.google.com/

### Step 2: Create/Select Project
- Click "Select a project" → "New Project"
- Name: `YouTube Transcript Downloader`
- Click "Create"

### Step 3: Enable YouTube Data API
- Go to "APIs & Services" → "Library"
- Search for "YouTube Data API v3"
- Click on it → Click "Enable"

### Step 4: Create API Key
- Go to "APIs & Services" → "Credentials"
- Click "Create Credentials" → "API Key"
- Copy the API key (looks like: `AIzaSyC...`)

### Step 5: (Optional) Restrict API Key
- Click on your API key → "Restrict Key"
- Under "API restrictions" → Select "YouTube Data API v3"
- Save

## Usage

### Option 1: Set Environment Variable (Recommended)
```bash
export YOUTUBE_API_KEY="AIzaSyAi-QCNTEEbZFNnw-kCL9_ELp8jdwoN7-s"
python3 youtube_api_transcript_downloader.py
```

### Option 2: Enter When Prompted
```bash
python3 youtube_api_transcript_downloader.py
# Script will ask for API key
```

## API Quotas

- **Free tier**: 10,000 units/day
- **Each playlist**: ~1-5 units
- **Each video**: ~1 unit
- **You can process 1000+ videos per day easily**

## Troubleshooting

### "API key not valid"
- Make sure you copied the full key
- Check that YouTube Data API v3 is enabled
- Wait 1-2 minutes after creating the key

### "Quota exceeded"
- You've used your daily 10,000 units
- Resets at midnight Pacific Time
- Consider requesting quota increase if needed

### "Access forbidden"
- Make sure the playlist is public
- Check API key restrictions

## Cost

- **Free**: 10,000 units/day
- **Paid**: $0.50 per 1,000 additional units
- **For most users**: Free tier is more than enough

## Security

- ✅ Keep your API key private
- ✅ Don't commit it to version control
- ✅ Use environment variables
- ✅ Restrict the key to YouTube Data API only

---

**Total setup time: ~5 minutes**
**Result: No more rate limiting issues!** 🎉

#!/usr/bin/env python3
"""
YouTube API Transcript Downloader

Uses official YouTube Data API v3 for playlist info and yt-dlp for transcripts.
This approach has much higher rate limits and is more reliable.
"""

import os
import re
import ssl
import sys
import time
import json
import tempfile
from pathlib import Path
from typing import List, Optional, Tuple

try:
    import yt_dlp
    from googleapiclient.discovery import build
    from googleapiclient.errors import HttpError
    from deep_translator import GoogleTranslator
except ImportError as e:
    print(f"Error: Missing required library. {e}")
    print("Please install required packages with:")
    print("pip install yt-dlp google-api-python-client deep-translator")
    sys.exit(1)

class YouTubeAPITranscriptDownloader:
    """Downloads transcripts using YouTube Data API v3 + yt-dlp."""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.youtube = build('youtube', 'v3', developerKey=api_key)
        
        # Configure SSL for macOS
        self._configure_ssl()
        
        # Configure yt-dlp for subtitle extraction
        self.ydl_opts_subtitles = {
            'writesubtitles': True,
            'writeautomaticsub': True,
            'subtitleslangs': ['en', 'en-US', 'en-GB', 'hi', 'es', 'fr'],
            'subtitlesformat': 'vtt',
            'skip_download': True,
            'quiet': True,
            'no_warnings': True,
        }
    
    def _configure_ssl(self):
        """Configure SSL to handle certificate issues on macOS."""
        try:
            ssl.create_default_context()
        except Exception:
            ssl._create_default_https_context = ssl._create_unverified_context
            print("⚠️  SSL certificate verification disabled (macOS compatibility)")
    
    def sanitize_filename(self, title: str) -> str:
        """Sanitize title for use as filename."""
        sanitized = re.sub(r'[<>:"/\\|?*]', '', title)
        sanitized = re.sub(r'\s+', '_', sanitized)
        sanitized = sanitized[:100]
        sanitized = sanitized.rstrip('. ')
        return sanitized if sanitized else "untitled_video"
    
    def extract_playlist_id(self, url: str) -> Optional[str]:
        """Extract playlist ID from YouTube URL."""
        import re
        patterns = [
            r'list=([a-zA-Z0-9_-]+)',
            r'playlist\?list=([a-zA-Z0-9_-]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        return None
    
    def get_playlist_info(self, playlist_url: str) -> Tuple[str, List[Tuple[str, str]]]:
        """
        Get playlist information using YouTube Data API.
        
        Returns:
            Tuple of (playlist_title, list of (video_id, title) tuples)
        """
        playlist_id = self.extract_playlist_id(playlist_url)
        if not playlist_id:
            raise ValueError("Invalid playlist URL")
        
        try:
            # Get playlist details
            playlist_response = self.youtube.playlists().list(
                part='snippet',
                id=playlist_id
            ).execute()
            
            if not playlist_response['items']:
                raise ValueError("Playlist not found")
            
            playlist_title = playlist_response['items'][0]['snippet']['title']
            playlist_title = self.sanitize_filename(playlist_title)
            
            # Get all videos in playlist
            videos = []
            next_page_token = None
            
            while True:
                playlist_items = self.youtube.playlistItems().list(
                    part='snippet',
                    playlistId=playlist_id,
                    maxResults=50,
                    pageToken=next_page_token
                ).execute()
                
                for item in playlist_items['items']:
                    video_id = item['snippet']['resourceId']['videoId']
                    video_title = item['snippet']['title']
                    videos.append((video_id, video_title))
                
                next_page_token = playlist_items.get('nextPageToken')
                if not next_page_token:
                    break
            
            return playlist_title, videos
            
        except HttpError as e:
            if e.resp.status == 403:
                raise Exception("API quota exceeded or invalid API key")
            else:
                raise Exception(f"YouTube API error: {e}")
    
    def extract_text_from_vtt(self, vtt_content: str) -> str:
        """Extract plain text from VTT subtitle content."""
        lines = vtt_content.split('\n')
        text_lines = []
        
        for line in lines:
            line = line.strip()
            if (line and 
                not line.startswith('WEBVTT') and 
                not line.startswith('NOTE') and
                not '-->' in line and
                not line.isdigit() and
                not line.startswith('STYLE') and
                not line.startswith('::cue')):
                
                clean_line = re.sub(r'<[^>]+>', '', line)
                if clean_line.strip():
                    text_lines.append(clean_line.strip())
        
        return '\n'.join(text_lines)
    
    def download_subtitles(self, video_id: str, temp_dir: str) -> Optional[Tuple[str, str]]:
        """Download subtitles using yt-dlp."""
        try:
            video_url = f"https://www.youtube.com/watch?v={video_id}"
            opts = self.ydl_opts_subtitles.copy()
            opts['outtmpl'] = os.path.join(temp_dir, '%(title)s.%(ext)s')
            
            with yt_dlp.YoutubeDL(opts) as ydl:
                info = ydl.extract_info(video_url, download=False)
                
                if not info:
                    return None
                
                subtitles = info.get('subtitles', {})
                automatic_captions = info.get('automatic_captions', {})
                all_subs = {**automatic_captions, **subtitles}
                
                if not all_subs:
                    return None
                
                # Priority order for languages
                lang_priority = ['en', 'en-US', 'en-GB', 'hi', 'es', 'fr']
                selected_lang = None
                
                for lang in lang_priority:
                    if lang in all_subs:
                        selected_lang = lang
                        break
                
                if not selected_lang:
                    selected_lang = list(all_subs.keys())[0]
                
                # Download subtitles
                opts['subtitleslangs'] = [selected_lang]
                with yt_dlp.YoutubeDL(opts) as ydl_download:
                    ydl_download.download([video_url])
                
                # Find and read the subtitle file
                for file in os.listdir(temp_dir):
                    if file.endswith(f'.{selected_lang}.vtt'):
                        subtitle_path = os.path.join(temp_dir, file)
                        with open(subtitle_path, 'r', encoding='utf-8') as f:
                            vtt_content = f.read()
                        
                        text = self.extract_text_from_vtt(vtt_content)
                        return text, selected_lang
                
                return None
                
        except Exception as e:
            print(f"    ❌ Error downloading subtitles: {e}")
            return None
    
    def translate_text(self, text: str, source_lang: str, target_lang: str) -> Optional[str]:
        """Translate text from source to target language."""
        try:
            if source_lang.startswith(target_lang):
                return text
            
            lang_map = {
                'en-US': 'en', 'en-GB': 'en', 'en-CA': 'en', 'en-AU': 'en',
                'fr-FR': 'fr', 'fr-CA': 'fr',
                'hi': 'hi', 'es': 'es'
            }
            
            source = lang_map.get(source_lang, source_lang)
            target = lang_map.get(target_lang, target_lang)
            
            if source == target:
                return text
            
            translator = GoogleTranslator(source=source, target=target)
            
            # Handle long texts by chunking
            max_chunk_size = 4500
            if len(text) <= max_chunk_size:
                return translator.translate(text)
            
            chunks = []
            lines = text.split('\n')
            current_chunk = ""
            
            for line in lines:
                if len(current_chunk + line + '\n') <= max_chunk_size:
                    current_chunk += line + '\n'
                else:
                    if current_chunk:
                        chunks.append(current_chunk.strip())
                    current_chunk = line + '\n'
            
            if current_chunk:
                chunks.append(current_chunk.strip())
            
            translated_chunks = []
            for chunk in chunks:
                try:
                    result = translator.translate(chunk)
                    translated_chunks.append(result)
                    time.sleep(0.1)
                except Exception:
                    translated_chunks.append(chunk)
            
            return '\n'.join(translated_chunks)
            
        except Exception as e:
            print(f"    ⚠️  Translation error: {e}")
            return None
    
    def process_video(self, video_id: str, video_title: str, output_dir: Path) -> bool:
        """Process a single video and save transcripts."""
        print(f"    🆔 Video ID: {video_id}")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            result = self.download_subtitles(video_id, temp_dir)
            if not result:
                print(f"    ❌ No subtitles available")
                return False
            
            original_text, lang_code = result
            print(f"    📝 Found subtitles in: {lang_code}")
            
            # Create directories
            english_dir = output_dir / "english"
            french_dir = output_dir / "french"
            english_dir.mkdir(exist_ok=True)
            french_dir.mkdir(exist_ok=True)
            
            success = False
            
            # Handle English
            if lang_code.startswith('en'):
                # Save English
                english_file = english_dir / f"{video_title}_en.txt"
                with open(english_file, 'w', encoding='utf-8') as f:
                    f.write(f"Video Title: {video_title}\n")
                    f.write(f"Video ID: {video_id}\n")
                    f.write(f"Language: English ({lang_code})\n")
                    f.write("-" * 50 + "\n\n")
                    f.write(original_text)
                print(f"    ✅ English transcript saved")
                success = True
                
                # Translate to French
                french_text = self.translate_text(original_text, 'en', 'fr')
                if french_text:
                    french_file = french_dir / f"{video_title}_fr.txt"
                    with open(french_file, 'w', encoding='utf-8') as f:
                        f.write(f"Video Title: {video_title}\n")
                        f.write(f"Video ID: {video_id}\n")
                        f.write(f"Language: French (translated)\n")
                        f.write("-" * 50 + "\n\n")
                        f.write(french_text)
                    print(f"    ✅ French transcript saved (translated)")
            else:
                # Translate to English first
                english_text = self.translate_text(original_text, lang_code, 'en')
                if english_text:
                    english_file = english_dir / f"{video_title}_en.txt"
                    with open(english_file, 'w', encoding='utf-8') as f:
                        f.write(f"Video Title: {video_title}\n")
                        f.write(f"Video ID: {video_id}\n")
                        f.write(f"Language: English (translated from {lang_code})\n")
                        f.write("-" * 50 + "\n\n")
                        f.write(english_text)
                    print(f"    ✅ English transcript saved (translated)")
                    success = True
                    
                    # Translate to French
                    french_text = self.translate_text(english_text, 'en', 'fr')
                    if french_text:
                        french_file = french_dir / f"{video_title}_fr.txt"
                        with open(french_file, 'w', encoding='utf-8') as f:
                            f.write(f"Video Title: {video_title}\n")
                            f.write(f"Video ID: {video_id}\n")
                            f.write(f"Language: French (translated)\n")
                            f.write("-" * 50 + "\n\n")
                            f.write(french_text)
                        print(f"    ✅ French transcript saved (translated)")
            
            return success

def get_api_key():
    """Get YouTube API key from user or environment."""
    # Check environment variable first
    api_key = os.environ.get('YOUTUBE_API_KEY')
    if api_key:
        return api_key
    
    print("🔑 YouTube Data API v3 Setup Required")
    print("=" * 50)
    print("To use this script, you need a YouTube Data API v3 key.")
    print()
    print("📋 Quick Setup (5 minutes):")
    print("1. Go to: https://console.developers.google.com/")
    print("2. Create a new project (or select existing)")
    print("3. Enable 'YouTube Data API v3'")
    print("4. Create credentials (API Key)")
    print("5. Copy the API key")
    print()
    
    api_key = input("Please paste your YouTube API key: ").strip()
    if not api_key:
        print("❌ No API key provided. Exiting.")
        sys.exit(1)
    
    return api_key

def main():
    """Main function."""
    print("🎯 YouTube API Transcript Downloader")
    print("📝 Uses official YouTube Data API v3 (higher rate limits)")
    print("=" * 60)
    
    # Get API key
    api_key = get_api_key()
    
    # Get playlist URL
    playlist_url = input("Please paste the YouTube playlist URL: ").strip()
    if not playlist_url:
        print("❌ No URL provided. Exiting.")
        return
    
    try:
        downloader = YouTubeAPITranscriptDownloader(api_key)
        
        # Get playlist info using API
        print("📋 Getting playlist information via YouTube API...")
        playlist_title, videos = downloader.get_playlist_info(playlist_url)
        
        if not videos:
            print("❌ No videos found in playlist")
            return
        
        # Setup output directory
        output_dir = Path("transcripts") / playlist_title
        output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"📋 Playlist: {playlist_title}")
        print(f"📹 Found {len(videos)} videos")
        print(f"📁 Saving to: {output_dir.absolute()}")
        print()
        
        success_count = 0
        
        for i, (video_id, title) in enumerate(videos, 1):
            sanitized_title = downloader.sanitize_filename(title)
            print(f"[{i}/{len(videos)}] Processing: {title}")
            
            if downloader.process_video(video_id, sanitized_title, output_dir):
                success_count += 1
            
            print()
        
        print("=" * 60)
        print(f"📊 Successfully processed: {success_count}/{len(videos)} videos")
        print(f"📁 Files saved in: {output_dir.absolute()}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()

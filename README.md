# YouTube Playlist Transcript Downloader

A robust Python script that downloads English and French transcripts for all videos in a YouTube playlist. The script handles SSL certificate errors gracefully (especially on macOS) and organizes transcripts into language-specific folders.

## Features

- ✅ Downloads transcripts for entire YouTube playlists
- ✅ Supports English and French languages with multiple regional variants
- ✅ **Auto-translates English transcripts to French** when native French transcripts aren't available
- ✅ **Playlist-specific folder naming** - automatically creates folders based on playlist title
- ✅ Handles SSL certificate issues on macOS automatically
- ✅ Robust error handling for missing transcripts
- ✅ Organizes files into language-specific folders
- ✅ Sanitizes filenames for cross-platform compatibility
- ✅ Progress tracking and detailed logging
- ✅ Both interactive and programmatic usage
- ✅ Smart chunking for long transcripts during translation

## Installation

### Quick Setup (Recommended)

**For macOS/Linux:**
```bash
chmod +x setup.sh
./setup.sh
```

**For Windows:**
```cmd
setup.bat
```

### Manual Installation

1. **Ensure Python 3.7+ is installed**

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

   Or install manually:
   ```bash
   pip install yt-dlp youtube-transcript-api deep-translator
   ```

## Usage

### Interactive Mode

Run the main script and enter a playlist URL when prompted:

```bash
python youtube_playlist_transcripts.py
```

### Programmatic Usage

Use the downloader in your own scripts:

```python
from youtube_playlist_transcripts import YouTubePlaylistTranscriptDownloader

# Create downloader instance
downloader = YouTubePlaylistTranscriptDownloader(base_dir="my_transcripts")

# Download transcripts
playlist_url = "https://www.youtube.com/playlist?list=YOUR_PLAYLIST_ID"
downloader.download_playlist_transcripts(playlist_url)
```

## Supported URL Formats

The script accepts various YouTube playlist URL formats:

- `https://www.youtube.com/playlist?list=PLAYLIST_ID`
- `https://www.youtube.com/watch?v=VIDEO_ID&list=PLAYLIST_ID`
- `https://youtu.be/VIDEO_ID?list=PLAYLIST_ID`

## Output Structure

Transcripts are saved in the following structure:

```
transcripts/
└── [Playlist_Name]/
    ├── english/
    │   ├── Video_Title_1_en.txt
    │   ├── Video_Title_2_en.txt
    │   └── ...
    └── french/
        ├── Video_Title_1_fr.txt
        ├── Video_Title_2_fr.txt
        └── ...
```

**Example with actual playlist:**
```
transcripts/
└── Starting_a_Career_in_Data_Science_Project_Portfolio_Resume_and_Interview_Process/
    ├── english/
    │   ├── 1_1_Course_Overview_en.txt
    │   ├── 1_2_The_Data_Science_Knowledge_You_Need_en.txt
    │   └── ...
    └── french/
        ├── 1_1_Course_Overview_fr.txt
        ├── 1_2_The_Data_Science_Knowledge_You_Need_fr.txt
        └── ...
```

Each transcript file includes:
- Video title and ID
- Language information (including whether it's translated)
- Full transcript text

## Translation Feature

When French transcripts are not natively available for a video, the script automatically:

1. **Downloads the English transcript** first
2. **Translates it to French** using Google Translate API
3. **Saves the translated version** in the French folder
4. **Marks it as translated** in the file header

Translation features:
- ✅ **Smart chunking**: Handles long transcripts by splitting them into manageable chunks
- ✅ **Rate limiting**: Includes delays to avoid API rate limits
- ✅ **Error handling**: Falls back gracefully if translation fails
- ✅ **Quality preservation**: Maintains sentence structure during chunking

## Language Support

### English Variants
- `en` (Generic English)
- `en-US` (US English)
- `en-GB` (British English)
- `en-CA` (Canadian English)
- `en-AU` (Australian English)

### French Variants
- `fr` (Generic French)
- `fr-FR` (France French)
- `fr-CA` (Canadian French)

## Error Handling

The script gracefully handles:

- **SSL Certificate Issues**: Automatically configures SSL for macOS compatibility
- **Missing Transcripts**: Continues processing other videos when transcripts are unavailable
- **Network Errors**: Provides clear error messages and continues where possible
- **Invalid URLs**: Validates playlist URLs before processing
- **Disabled Transcripts**: Handles videos where transcripts are disabled by the creator

## Requirements

- Python 3.7+ (including Python 3.13+)
- `yt-dlp` (modern YouTube downloader)
- `youtube-transcript-api` (transcript extraction)
- `deep-translator` (translation service)

## Troubleshooting

### SSL Certificate Errors on macOS

The script automatically handles SSL certificate issues common on macOS. If you still encounter SSL errors, try:

```bash
/Applications/Python\ 3.x/Install\ Certificates.command
```

### No Transcripts Found

- Ensure the playlist is public
- Check if videos have captions enabled
- Some videos may not have transcripts in the requested languages

### Permission Errors

Make sure you have write permissions in the directory where you're running the script.

## Contributing

Feel free to submit issues and enhancement requests!

## License

This project is open source and available under the MIT License.



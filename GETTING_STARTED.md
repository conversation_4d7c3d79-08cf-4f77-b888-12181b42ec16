# 🚀 Getting Started Guide

## For Recipients of This Project

If someone shared this YouTube Playlist Transcript Downloader with you, follow these simple steps to get it running:

### Step 1: Prerequisites ✅

Make sure you have **Python 3.7 or newer** installed:

**Check if Python is installed:**
```bash
python3 --version
```
or on Windows:
```cmd
python --version
```

**If Python is not installed:**
- **Windows/Mac**: Download from [python.org](https://www.python.org/downloads/)
- **Linux**: Use your package manager (e.g., `sudo apt install python3`)

### Step 2: Quick Setup 🛠️

**Option A: Automatic Setup (Easiest)**

**On macOS/Linux:**
```bash
chmod +x setup.sh
./setup.sh
```

**On Windows:**
```cmd
setup.bat
```

**Option B: Manual Setup**
```bash
pip install -r requirements.txt
```

### Step 3: Run the Script 🎬

```bash
python3 youtube_playlist_transcripts.py
```

When prompted, paste your YouTube playlist URL and press Enter.

### Step 4: Results 📁

The script will create a `transcripts/[playlist_name]/` folder with:
- `english/` - Original English transcripts
- `french/` - Auto-translated French transcripts

**Example:** For a playlist called "Data Science Course", you'll get:
```
transcripts/Data_Science_Course/
├── english/
└── french/
```

## Example Usage

```bash
$ python3 youtube_playlist_transcripts.py

Please paste the YouTube playlist URL: https://www.youtube.com/playlist?list=YOUR_PLAYLIST_ID

🎬 YouTube Playlist Transcript Downloader
==================================================
📋 Extracting playlist information...
📹 Found 10 videos in playlist
📁 Saving transcripts to: /path/to/transcripts

[1/10] Processing: Video Title
    🆔 Video ID: abc123
    ✅ English transcript saved (en)
    🌐 No native French transcript, attempting translation...
    ✅ French transcript saved (translated)
```

## Troubleshooting 🔧

### Common Issues

**SSL Certificate Error (macOS):**
```bash
/Applications/Python\ 3.x/Install\ Certificates.command
```

**Permission Denied:**
```bash
chmod +x setup.sh
```

**Import Errors:**
- Try `pip3` instead of `pip`
- Try `python` instead of `python3`
- Reinstall dependencies: `pip install -r requirements.txt`

**No Videos Found:**
- Make sure the playlist is public
- Check the URL format
- Try a different playlist

### Getting Help

If you encounter issues:
1. Check that all dependencies are installed
2. Verify your Python version (3.7+)
3. Make sure you have internet connection
4. Try with a different, smaller playlist first

## Features Overview 🌟

- ✅ Downloads English transcripts from YouTube
- ✅ Auto-translates to French when native French isn't available
- ✅ **Playlist-specific folder naming** - no more confusion between different playlists
- ✅ Handles SSL certificate issues automatically
- ✅ Organizes files in language-specific folders
- ✅ Progress tracking with clear status messages
- ✅ Robust error handling

## File Structure After Running

```
youtube_playlist_transc/
├── transcripts/
│   └── [Playlist_Name]/
│       ├── english/
│       │   ├── Video_1_en.txt
│       │   ├── Video_2_en.txt
│       │   └── ...
│       └── french/
│           ├── Video_1_fr.txt
│           ├── Video_2_fr.txt
│           └── ...
├── youtube_playlist_transcripts.py
├── requirements.txt
└── README.md
```

That's it! You're ready to download and translate YouTube playlist transcripts! 🎉

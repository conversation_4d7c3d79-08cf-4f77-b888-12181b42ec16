#!/usr/bin/env python3
"""
YouTube Playlist Transcript Downloader using yt-dlp (No Rate Limits)

This version uses yt-dlp's subtitle extraction instead of the transcript API,
which bypasses YouTube's transcript API rate limits.
"""

import os
import re
import ssl
import sys
import time
import json
import tempfile
from pathlib import Path
from typing import List, Optional, Tuple

try:
    import yt_dlp
    from deep_translator import GoogleTranslator
except ImportError as e:
    print(f"Error: Missing required library. {e}")
    print("Please install required packages with:")
    print("pip install yt-dlp deep-translator")
    sys.exit(1)

class YtDlpTranscriptDownloader:
    """Downloads transcripts using yt-dlp subtitle extraction (bypasses API rate limits)."""
    
    def __init__(self):
        # Configure SSL for macOS
        self._configure_ssl()
        
        # Configure yt-dlp for subtitle extraction
        self.ydl_opts_subtitles = {
            'writesubtitles': True,
            'writeautomaticsub': True,
            'subtitleslangs': ['en', 'en-US', 'en-GB', 'hi', 'es', 'fr'],  # Multiple languages
            'subtitlesformat': 'vtt',
            'skip_download': True,  # Don't download video, just subtitles
            'quiet': True,
            'no_warnings': True,
        }
        
        self.ydl_opts_info = {
            'quiet': True,
            'no_warnings': True,
            'extract_flat': True,
        }
    
    def _configure_ssl(self):
        """Configure SSL to handle certificate issues on macOS."""
        try:
            ssl.create_default_context()
        except Exception:
            ssl._create_default_https_context = ssl._create_unverified_context
            print("⚠️  SSL certificate verification disabled (macOS compatibility)")
    
    def sanitize_filename(self, title: str) -> str:
        """Sanitize title for use as filename."""
        sanitized = re.sub(r'[<>:"/\\|?*]', '', title)
        sanitized = re.sub(r'\s+', '_', sanitized)
        sanitized = sanitized[:100]
        sanitized = sanitized.rstrip('. ')
        return sanitized if sanitized else "untitled_video"
    
    def extract_text_from_vtt(self, vtt_content: str) -> str:
        """Extract plain text from VTT subtitle content."""
        lines = vtt_content.split('\n')
        text_lines = []
        
        for line in lines:
            line = line.strip()
            # Skip VTT headers, timestamps, and empty lines
            if (line and 
                not line.startswith('WEBVTT') and 
                not line.startswith('NOTE') and
                not '-->' in line and
                not line.isdigit() and
                not line.startswith('STYLE') and
                not line.startswith('::cue')):
                
                # Remove HTML tags
                clean_line = re.sub(r'<[^>]+>', '', line)
                if clean_line.strip():
                    text_lines.append(clean_line.strip())
        
        return '\n'.join(text_lines)
    
    def download_subtitles(self, video_url: str, temp_dir: str) -> Optional[Tuple[str, str]]:
        """
        Download subtitles for a video using yt-dlp.
        
        Returns:
            Tuple of (subtitle_text, language_code) or None
        """
        try:
            # Set output template to temp directory
            opts = self.ydl_opts_subtitles.copy()
            opts['outtmpl'] = os.path.join(temp_dir, '%(title)s.%(ext)s')
            
            with yt_dlp.YoutubeDL(opts) as ydl:
                # Extract info first to get available subtitles
                info = ydl.extract_info(video_url, download=False)
                
                if not info:
                    return None
                
                # Check available subtitles
                subtitles = info.get('subtitles', {})
                automatic_captions = info.get('automatic_captions', {})
                
                # Combine both subtitle sources
                all_subs = {**automatic_captions, **subtitles}
                
                if not all_subs:
                    print(f"    ❌ No subtitles available")
                    return None
                
                # Priority order for languages
                lang_priority = ['en', 'en-US', 'en-GB', 'hi', 'es', 'fr']
                
                selected_lang = None
                for lang in lang_priority:
                    if lang in all_subs:
                        selected_lang = lang
                        break
                
                if not selected_lang:
                    # Use first available language
                    selected_lang = list(all_subs.keys())[0]
                
                print(f"    📝 Found subtitles in: {selected_lang}")
                
                # Download subtitles
                opts['subtitleslangs'] = [selected_lang]
                with yt_dlp.YoutubeDL(opts) as ydl_download:
                    ydl_download.download([video_url])
                
                # Find the downloaded subtitle file
                for file in os.listdir(temp_dir):
                    if file.endswith(f'.{selected_lang}.vtt'):
                        subtitle_path = os.path.join(temp_dir, file)
                        with open(subtitle_path, 'r', encoding='utf-8') as f:
                            vtt_content = f.read()
                        
                        text = self.extract_text_from_vtt(vtt_content)
                        return text, selected_lang
                
                return None
                
        except Exception as e:
            print(f"    ❌ Error downloading subtitles: {e}")
            return None
    
    def translate_text(self, text: str, source_lang: str, target_lang: str) -> Optional[str]:
        """Translate text from source to target language."""
        try:
            # Skip translation if already in target language
            if source_lang.startswith(target_lang):
                return text
            
            # Map language codes
            lang_map = {
                'en-US': 'en', 'en-GB': 'en', 'en-CA': 'en', 'en-AU': 'en',
                'fr-FR': 'fr', 'fr-CA': 'fr',
                'hi': 'hi', 'es': 'es'
            }
            
            source = lang_map.get(source_lang, source_lang)
            target = lang_map.get(target_lang, target_lang)
            
            if source == target:
                return text
            
            translator = GoogleTranslator(source=source, target=target)
            
            # Handle long texts by chunking
            max_chunk_size = 4500
            if len(text) <= max_chunk_size:
                return translator.translate(text)
            
            # Split into chunks
            chunks = []
            lines = text.split('\n')
            current_chunk = ""
            
            for line in lines:
                if len(current_chunk + line + '\n') <= max_chunk_size:
                    current_chunk += line + '\n'
                else:
                    if current_chunk:
                        chunks.append(current_chunk.strip())
                    current_chunk = line + '\n'
            
            if current_chunk:
                chunks.append(current_chunk.strip())
            
            # Translate chunks
            translated_chunks = []
            for chunk in chunks:
                try:
                    result = translator.translate(chunk)
                    translated_chunks.append(result)
                    time.sleep(0.1)  # Small delay
                except Exception:
                    translated_chunks.append(chunk)  # Fallback to original
            
            return '\n'.join(translated_chunks)
            
        except Exception as e:
            print(f"    ⚠️  Translation error: {e}")
            return None
    
    def process_video(self, video_id: str, video_title: str, output_dir: Path) -> bool:
        """Process a single video and save transcripts."""
        video_url = f"https://www.youtube.com/watch?v={video_id}"
        print(f"    🆔 Video ID: {video_id}")
        
        # Create temporary directory for subtitle files
        with tempfile.TemporaryDirectory() as temp_dir:
            # Download subtitles
            result = self.download_subtitles(video_url, temp_dir)
            if not result:
                return False
            
            original_text, lang_code = result
            
            # Create language-specific directories
            english_dir = output_dir / "english"
            french_dir = output_dir / "french"
            english_dir.mkdir(exist_ok=True)
            french_dir.mkdir(exist_ok=True)
            
            success = False
            
            # Handle English
            if lang_code.startswith('en'):
                # Save original English
                english_file = english_dir / f"{video_title}_en.txt"
                with open(english_file, 'w', encoding='utf-8') as f:
                    f.write(f"Video Title: {video_title}\n")
                    f.write(f"Video ID: {video_id}\n")
                    f.write(f"Language: English ({lang_code})\n")
                    f.write("-" * 50 + "\n\n")
                    f.write(original_text)
                print(f"    ✅ English transcript saved")
                success = True
                
                # Translate to French
                french_text = self.translate_text(original_text, 'en', 'fr')
                if french_text:
                    french_file = french_dir / f"{video_title}_fr.txt"
                    with open(french_file, 'w', encoding='utf-8') as f:
                        f.write(f"Video Title: {video_title}\n")
                        f.write(f"Video ID: {video_id}\n")
                        f.write(f"Language: French (translated from {lang_code})\n")
                        f.write("-" * 50 + "\n\n")
                        f.write(french_text)
                    print(f"    ✅ French transcript saved (translated)")
            else:
                # Translate to English first
                english_text = self.translate_text(original_text, lang_code, 'en')
                if english_text:
                    english_file = english_dir / f"{video_title}_en.txt"
                    with open(english_file, 'w', encoding='utf-8') as f:
                        f.write(f"Video Title: {video_title}\n")
                        f.write(f"Video ID: {video_id}\n")
                        f.write(f"Language: English (translated from {lang_code})\n")
                        f.write("-" * 50 + "\n\n")
                        f.write(english_text)
                    print(f"    ✅ English transcript saved (translated from {lang_code})")
                    success = True
                    
                    # Translate to French
                    french_text = self.translate_text(english_text, 'en', 'fr')
                    if french_text:
                        french_file = french_dir / f"{video_title}_fr.txt"
                        with open(french_file, 'w', encoding='utf-8') as f:
                            f.write(f"Video Title: {video_title}\n")
                            f.write(f"Video ID: {video_id}\n")
                            f.write(f"Language: French (translated from {lang_code} via English)\n")
                            f.write("-" * 50 + "\n\n")
                            f.write(french_text)
                        print(f"    ✅ French transcript saved (translated)")
            
            return success

def main():
    """Main function."""
    print("🚀 yt-dlp Transcript Downloader (No Rate Limits!)")
    print("📝 Uses yt-dlp subtitle extraction instead of YouTube API")
    print("=" * 60)
    
    playlist_url = input("Please paste the YouTube playlist URL: ").strip()
    if not playlist_url:
        print("❌ No URL provided. Exiting.")
        return
    
    downloader = YtDlpTranscriptDownloader()
    
    try:
        # Get playlist info
        with yt_dlp.YoutubeDL(downloader.ydl_opts_info) as ydl:
            playlist_info = ydl.extract_info(playlist_url, download=False)
            
            if 'entries' not in playlist_info:
                print("❌ No videos found in playlist")
                return
            
            playlist_title = playlist_info.get('title', 'Unknown_Playlist')
            playlist_title = downloader.sanitize_filename(playlist_title)
            
            videos = []
            for entry in playlist_info['entries']:
                if entry and 'id' in entry and 'title' in entry:
                    videos.append((entry['id'], entry['title']))
        
        if not videos:
            print("❌ No videos found in playlist")
            return
        
        # Setup output directory
        output_dir = Path("transcripts") / playlist_title
        output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"📋 Playlist: {playlist_title}")
        print(f"📹 Found {len(videos)} videos")
        print(f"📁 Saving to: {output_dir.absolute()}")
        print()
        
        success_count = 0
        
        for i, (video_id, title) in enumerate(videos, 1):
            sanitized_title = downloader.sanitize_filename(title)
            print(f"[{i}/{len(videos)}] Processing: {title}")
            
            if downloader.process_video(video_id, sanitized_title, output_dir):
                success_count += 1
            
            print()
        
        print("=" * 60)
        print(f"📊 Successfully processed: {success_count}/{len(videos)} videos")
        print(f"📁 Files saved in: {output_dir.absolute()}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
